# DAEIRA TTS Service - Enhanced Text-to-Speech Implementation

import logging
import asyncio
import threading
import uuid
import queue
from typing import Optional, Dict, Any, List
from enum import Enum
import time

# Try to import Windows SAPI
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False

logger = logging.getLogger("daeira.tts")

class TTSPriority(Enum):
    LOW = 0
    NORMAL = 1
    HIGH = 2
    URGENT = 3

class TTSMessage:
    """Represents a TTS message in the queue"""
    def __init__(self, text: str, priority: TTSPriority, message_id: str):
        self.text = text
        self.priority = priority
        self.message_id = message_id
        self.timestamp = time.time()

class TTSService:
    """
    Enhanced Text-to-Speech service for DAEIRA.
    Uses Windows SAPI with voice preferences: 'susan' (default), 'hazel' (fallback)
    """

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.is_initialized = False
        self.current_message_id = None
        self.is_speaking = False
        self.engine = None
        self.speaking_thread = None
        self.message_queue = queue.PriorityQueue()
        self.stop_event = threading.Event()
        self.speaking_lock = threading.Lock()

        # Voice preferences: susan (default), hazel (fallback)
        self.voice_preferences = ['susan', 'hazel']
        self.selected_voice = None

        self.stats = {
            "messages_spoken": 0,
            "total_characters": 0,
            "errors": 0,
            "queue_processed": 0
        }

        # Try to initialize TTS system
        self._initialize_tts()

    def _initialize_tts(self) -> bool:
        """Initialize the TTS system using pyttsx3"""
        if not TTS_AVAILABLE:
            logger.warning("pyttsx3 not available - TTS functionality disabled")
            return False

        try:
            self.engine = pyttsx3.init()

            # Configure voice preferences
            self._configure_voice()

            # Set speech rate and volume
            rate = self.config.get('rate', 180)
            volume = self.config.get('volume', 0.8)

            self.engine.setProperty('rate', rate)
            self.engine.setProperty('volume', volume)

            # Start the speaking thread
            self.speaking_thread = threading.Thread(target=self._speaking_worker, daemon=True)
            self.speaking_thread.start()

            self.is_initialized = True
            logger.info(f"✓ TTS system initialized successfully with voice: {self.selected_voice}")
            return True

        except Exception as e:
            logger.error(f"TTS initialization failed: {e}")
            return False

    def _configure_voice(self):
        """Configure voice with preference for 'susan', fallback to 'hazel'"""
        if not self.engine:
            return

        try:
            voices = self.engine.getProperty('voices')
            logger.info(f"Available voices: {[voice.name for voice in voices]}")

            # Try to find preferred voices in order
            for preferred_voice in self.voice_preferences:
                for voice in voices:
                    if preferred_voice.lower() in voice.name.lower():
                        self.engine.setProperty('voice', voice.id)
                        self.selected_voice = voice.name
                        logger.info(f"Selected voice: {voice.name}")
                        return

            # Fallback: try to find any female voice
            for voice in voices:
                if any(keyword in voice.name.lower() for keyword in ['female', 'woman', 'zira', 'eva']):
                    self.engine.setProperty('voice', voice.id)
                    self.selected_voice = voice.name
                    logger.info(f"Selected fallback female voice: {voice.name}")
                    return

            # Last resort: use first available voice
            if voices:
                self.engine.setProperty('voice', voices[0].id)
                self.selected_voice = voices[0].name
                logger.info(f"Selected default voice: {voices[0].name}")

        except Exception as e:
            logger.error(f"Error configuring voice: {e}")

    def _speaking_worker(self):
        """Background thread that processes the TTS queue"""
        while not self.stop_event.is_set():
            try:
                # Get message from queue with timeout
                try:
                    priority, message = self.message_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # Process the message
                self._speak_message(message)
                self.message_queue.task_done()
                self.stats["queue_processed"] += 1

            except Exception as e:
                logger.error(f"Error in speaking worker: {e}")
                self.stats["errors"] += 1

    def _speak_message(self, message: TTSMessage):
        """Speak a single message"""
        if not self.engine:
            return

        with self.speaking_lock:
            try:
                self.is_speaking = True
                self.current_message_id = message.message_id

                logger.info(f"Speaking message {message.message_id}: {message.text[:50]}...")

                self.engine.say(message.text)
                self.engine.runAndWait()

                self.stats["messages_spoken"] += 1
                self.stats["total_characters"] += len(message.text)

            except Exception as e:
                logger.error(f"Error speaking message {message.message_id}: {e}")
                self.stats["errors"] += 1
            finally:
                self.is_speaking = False
                self.current_message_id = None

    async def speak_text(
        self,
        text: str,
        priority: TTSPriority = TTSPriority.NORMAL,
        async_mode: bool = True
    ) -> Optional[str]:
        """Speak text using the TTS system"""

        if not self.is_initialized:
            logger.warning("TTS not initialized - cannot speak text")
            return None

        if not text or not text.strip():
            logger.warning("Empty text provided for TTS")
            return None

        try:
            # Generate unique message ID
            message_id = str(uuid.uuid4())

            # Create TTS message
            message = TTSMessage(text, priority, message_id)

            # Add to queue with priority (lower number = higher priority)
            self.message_queue.put((priority.value, message))

            logger.info(f"TTS queued: {message_id} ({len(text)} chars, priority: {priority.name})")

            return message_id

        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"TTS speak error: {e}")
            return None

    def stop_speaking(self) -> bool:
        """Stop current TTS playback"""
        if not self.is_initialized:
            return False

        try:
            with self.speaking_lock:
                if self.engine and self.is_speaking:
                    self.engine.stop()
                    self.is_speaking = False
                    logger.info("TTS stopped")
            return True

        except Exception as e:
            logger.error(f"TTS stop error: {e}")
            return False

    def clear_queue(self) -> int:
        """Clear TTS queue and return number of cleared messages"""
        if not self.is_initialized:
            return 0

        try:
            cleared_count = 0
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    cleared_count += 1
                except queue.Empty:
                    break

            logger.info(f"TTS queue cleared: {cleared_count} messages")
            return cleared_count

        except Exception as e:
            logger.error(f"TTS clear queue error: {e}")
            return 0

    def get_status(self) -> Dict[str, Any]:
        """Get TTS system status"""
        if not self.is_initialized:
            return {
                "available": False,
                "message": "TTS system not initialized"
            }

        try:
            return {
                "available": True,
                "is_speaking": self.is_speaking,
                "queue_size": self.message_queue.qsize(),
                "statistics": self.stats.copy(),
                "current_message_id": self.current_message_id,
                "selected_voice": self.selected_voice,
                "voice_preferences": self.voice_preferences
            }

        except Exception as e:
            logger.error(f"TTS status error: {e}")
            return {
                "available": False,
                "error": str(e)
            }

    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get list of available TTS voices"""
        if not self.is_initialized or not self.engine:
            return []

        try:
            voices = self.engine.getProperty('voices')
            voice_list = []

            for voice in voices[:20]:  # Limit to first 20 for UI purposes
                voice_info = {
                    "id": voice.id,
                    "name": voice.name,
                    "languages": getattr(voice, 'languages', []),
                    "gender": getattr(voice, 'gender', 'unknown')
                }
                voice_list.append(voice_info)

            return voice_list

        except Exception as e:
            logger.error(f"Error getting TTS voices: {e}")
            return []

    def update_settings(
        self,
        rate: Optional[int] = None,
        volume: Optional[float] = None,
        voice_id: Optional[str] = None
    ) -> bool:
        """Update TTS settings"""
        if not self.is_initialized or not self.engine:
            return False

        try:
            if rate is not None:
                self.engine.setProperty('rate', rate)
                logger.info(f"TTS rate updated to: {rate}")

            if volume is not None:
                self.engine.setProperty('volume', volume)
                logger.info(f"TTS volume updated to: {volume}")

            if voice_id is not None:
                voices = self.engine.getProperty('voices')
                for voice in voices:
                    if voice.id == voice_id:
                        self.engine.setProperty('voice', voice_id)
                        self.selected_voice = voice.name
                        logger.info(f"TTS voice updated to: {voice.name}")
                        break

            return True

        except Exception as e:
            logger.error(f"TTS settings update error: {e}")
            return False

    def shutdown(self) -> None:
        """Shutdown TTS system"""
        if self.is_initialized:
            try:
                # Signal the worker thread to stop
                self.stop_event.set()

                # Stop any current speech
                self.stop_speaking()

                # Wait for worker thread to finish
                if self.speaking_thread and self.speaking_thread.is_alive():
                    self.speaking_thread.join(timeout=2.0)

                self.is_initialized = False
                logger.info("TTS system shutdown")

            except Exception as e:
                logger.error(f"TTS shutdown error: {e}")

# Global TTS service instance
_tts_service: Optional[TTSService] = None

def get_tts_service() -> Optional[TTSService]:
    """Get the global TTS service instance"""
    return _tts_service

def initialize_tts_service(config: Dict[str, Any] = None) -> TTSService:
    """Initialize the global TTS service with voice preferences"""
    global _tts_service

    # Default config with voice preferences
    default_config = {
        'rate': 180,
        'volume': 0.8,
        'voice_preference': 'susan,hazel',
        'max_text_length': 100000
    }

    if config:
        default_config.update(config)

    _tts_service = TTSService(default_config)
    return _tts_service

def get_tts_priority(priority_str: str) -> TTSPriority:
    """Convert string priority to TTSPriority enum"""
    priority_map = {
        "low": TTSPriority.LOW,
        "normal": TTSPriority.NORMAL,
        "high": TTSPriority.HIGH,
        "urgent": TTSPriority.URGENT
    }
    return priority_map.get(priority_str.lower(), TTSPriority.NORMAL)

# Convenience functions for easy integration
async def speak_text_async(text: str, priority: str = "normal") -> Optional[str]:
    """Convenience function to speak text asynchronously"""
    tts = get_tts_service()
    if tts:
        tts_priority = get_tts_priority(priority)
        return await tts.speak_text(text, tts_priority, async_mode=True)
    return None

def stop_tts():
    """Convenience function to stop TTS"""
    tts = get_tts_service()
    if tts:
        return tts.stop_speaking()
    return False

def clear_tts_queue():
    """Convenience function to clear TTS queue"""
    tts = get_tts_service()
    if tts:
        return tts.clear_queue()
    return 0

def get_tts_status():
    """Convenience function to get TTS status"""
    tts = get_tts_service()
    if tts:
        return tts.get_status()
    return {"available": False, "message": "TTS service not initialized"}