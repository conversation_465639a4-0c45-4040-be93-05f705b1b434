import React, { useState, useEffect, useRef } from "react";

const App = () => {
  const [messages, setMessages] = useState([
    {
      role: "",
      content: ""
    },
    {
      role: "<PERSON><PERSON>",
      mainContent: "",
      thoughtProcess: ""
    }
  ]);
  
  const [input, setInput] = useState("");
  const [isConnected, setIsConnected] = useState(true);
  const [logoHovered, setLogoHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // TTS-related state
  const [ttsEnabled, setTtsEnabled] = useState(true);
  const [ttsPriority, setTtsPriority] = useState("normal");
  const [ttsStatus, setTtsStatus] = useState({
    available: false,
    is_speaking: false,
    queue_size: 0
  });
  const [systemStatus, setSystemStatus] = useState({});
  
  // Image upload functionality
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  
  // Think content for logo overlay
  const [latestThinkContent, setLatestThinkContent] = useState('');
  
  // UNITARY, SINGULAR, CONTINUOUS - The eternal conversation
  const [conversationHistory, setConversationHistory] = useState(() => {
    try {
      const saved = localStorage.getItem('daeira_eternal_conversation');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Error loading eternal conversation:', error);
      return [];
    }
  });
  
  const chatRef = useRef(null);
  const statusCheckInterval = useRef(null);

  // Persist eternal conversation to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('daeira_eternal_conversation', JSON.stringify(conversationHistory));
  }, [conversationHistory]);

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
    }
  }, [messages]);

  // Check system status periodically
  useEffect(() => {
    const checkSystemStatus = async () => {
      try {
        const response = await fetch("http://localhost:8000/system_status");
        const status = await response.json();
        setSystemStatus(status);
      } catch (error) {
        console.error("Error checking system status:", error);
        setIsConnected(false);
      }
    };

    const checkTtsStatus = async () => {
      try {
        const response = await fetch("http://localhost:8000/tts/status");
        const status = await response.json();
        setTtsStatus(status);
      } catch (error) {
        console.error("Error checking TTS status:", error);
      }
    };

    // Initial checks
    checkSystemStatus();
    checkTtsStatus();

    // Set up periodic checking
    statusCheckInterval.current = setInterval(() => {
      checkSystemStatus();
      checkTtsStatus();
    }, 5000000000);

    return () => {
      if (statusCheckInterval.current) {
        clearInterval(statusCheckInterval.current);
      }
    };
  }, []);

  const sendMessage = async (useAdvancedMode = false) => {
    if ((!input.trim() && !imageFile) || isLoading) return;

    const userInput = input;
    setInput("");
    setIsLoading(true);
    

    const userMessage = {
      type: 'user',
      content: userInput,
      timestamp: new Date().toISOString(),
      hasImage: !!imageFile
    };
    
    const newMessage = { 
      role: "Victor", 
      content: userInput,
      imagePreview: imageFile ? imagePreview : null,
      imageName: imageFile ? imageFile.name : null
    };
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);
    

    const updatedHistory = [...conversationHistory, userMessage];
    setConversationHistory(updatedHistory);

    try {
      const endpoint = useAdvancedMode ? "/message" : "/chat";
      
      // Create FormData for the new form-based endpoint
      const formData = new FormData();
      formData.append('prompt', userInput);
      formData.append('enable_tts', ttsEnabled.toString());
      formData.append('tts_priority', ttsPriority);
      
      // Add image if present
      if (imageFile) {
        formData.append('image', imageFile);
        console.log(`Sending image: ${imageFile.name} (${imageFile.size} bytes)`);
      }
      
      const response = await fetch(`http://localhost:8000${endpoint}`, {
        method: "POST",
        body: formData  // No Content-Type header needed with FormData
      });

      const data = await response.json();
      
      console.log("API Response:", data);
      
      const assistantMessage = {
        role: "Daeira",
        mainContent: data.main_response || data.response || "No main response received",
        thoughtProcess: data.thought_process || data.thinking || "",
        ttsMessageId: data.tts_message_id || null,
        ttsEnabled: data.tts_enabled || false,
        ttsPriority: data.tts_priority || "normal",
        autoPlayed: !!data.tts_message_id  // Mark as auto-played if TTS was triggered
      };
      
      // Update latest think content for logo overlay
      if (assistantMessage.thoughtProcess) {
        console.log("Setting latest think content:", assistantMessage.thoughtProcess.substring(0, 100) + "...");
        setLatestThinkContent(assistantMessage.thoughtProcess);
      }
     
      const assistantHistoryMessage = {
        type: 'assistant',
        content: assistantMessage.mainContent,
        timestamp: new Date().toISOString()
      };
      
      const finalHistory = [...updatedHistory, assistantHistoryMessage];
      setConversationHistory(finalHistory);
      
      console.log("Assistant Message:", assistantMessage);
      
      setMessages([...updatedMessages, assistantMessage]);
      setIsConnected(true);
      
      // Clear image after successful send
      clearImage();
      
    } catch (error) {
      console.error("Error:", error);
      const errorMessage = { 
        role: "System", 
        content: "Connection error. Please try again." 
      };
      setMessages([...updatedMessages, errorMessage]);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (e.ctrlKey) {
        sendMessage(true); // Advanced mode with Ctrl+Enter
      } else {
        sendMessage(false); // Normal mode with Enter
      }
    }
  };

  const clearHistory = async () => {
    try {
      await fetch("http://localhost:8000/clear_history", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({})
      });
      setMessages([]);
      
      setConversationHistory([]);
      localStorage.removeItem('daeira_eternal_conversation');
    } catch (error) {
      console.error("Error clearing history:", error);
    }
  };

  const controlTTS = async (action) => {
    try {
      const formData = new FormData();
      formData.append('action', action);

      const response = await fetch("http://localhost:8000/tts/control", {
        method: "POST",
        body: formData
      });

      const result = await response.json();
      console.log(`TTS ${action} result:`, result);

      // Refresh TTS status after control action
      setTimeout(async () => {
        try {
          const statusResponse = await fetch("http://localhost:8000/tts/status");
          const status = await statusResponse.json();
          setTtsStatus(status);
        } catch (error) {
          console.error("Error refreshing TTS status:", error);
        }
      }, 500);

    } catch (error) {
      console.error(`Error controlling TTS (${action}):`, error);
    }
  };

  const speakText = async (text, priority = "normal") => {
    try {
      const formData = new FormData();
      formData.append('text', text);
      formData.append('priority', priority);
      formData.append('async_mode', 'true');

      const response = await fetch("http://localhost:8000/tts/speak", {
        method: "POST",
        body: formData
      });

      const result = await response.json();
      console.log("TTS speak result:", result);

      // Refresh TTS status to show speaking state
      setTimeout(async () => {
        try {
          const statusResponse = await fetch("http://localhost:8000/tts/status");
          const status = await statusResponse.json();
          setTtsStatus(status);
        } catch (error) {
          console.error("Error refreshing TTS status:", error);
        }
      }, 100);

    } catch (error) {
      console.error("Error speaking text:", error);
    }
  };

  // Email service functions
  const triggerEmailCheck = async () => {
    try {
      const response = await fetch("http://localhost:8000/email/check", {
        method: "POST"
      });

      const result = await response.json();
      console.log("Email check result:", result);

      // Show a brief notification or update status
      if (result.success) {
        console.log("Email check triggered successfully");
      }

    } catch (error) {
      console.error("Error triggering email check:", error);
    }
  };

  const sendTestEmail = async (toAddress, subject, message) => {
    try {
      const formData = new FormData();
      formData.append('to_address', toAddress);
      formData.append('subject', subject);
      formData.append('message', message);

      const response = await fetch("http://localhost:8000/email/send_test", {
        method: "POST",
        body: formData
      });

      const result = await response.json();
      console.log("Test email result:", result);

      return result.success;

    } catch (error) {
      console.error("Error sending test email:", error);
      return false;
    }
  };

  // Enhanced function to handle image upload
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Image file size must be less than 10MB');
      return;
    }

    setImageFile(file);

    // Create a preview URL for the uploaded image
    const previewUrl = URL.createObjectURL(file);
    setImagePreview(previewUrl);

    console.log(`Image selected: ${file.name} (${file.size} bytes)`);
  };

  // Function to clear selected image
  const clearImage = () => {
    setImageFile(null);
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview('');
    }
  };

  // New function to handle conversation history retrieval
  const handleHistoryRetrieval = async () => {
    try {
      const response = await fetch("http://localhost:8000/conversation_history", {
        method: "GET",
        headers: { "Content-Type": "application/json" }
      });

      const data = await response.json();
      console.log("Conversation history:", data);

      // Update the messages state with the retrieved history
      setMessages(data.history);
      setSessionId(data.session_id); // Assuming the session ID is returned
    } catch (error) {
      console.error("Error retrieving conversation history:", error);
    }
  };

  return (
    <div className="app">
      {/* Hero Section with Logo */}
      <div 
        className="hero-section"
        onMouseEnter={() => {
          console.log("Logo hovered, latest think content:", latestThinkContent ? "Available" : "None");
          setLogoHovered(true);
        }}
        onMouseLeave={() => setLogoHovered(false)}
      >
        <div className="logo-container">
          
          <div className={`overlay-content ${logoHovered ? 'visible' : ''}`}>
            <div className="overlay-text">
              {latestThinkContent ? (
                <>
                  <div className="think-header"></div>
                  <div className="think-body">{latestThinkContent}</div>
                </>
              ) : (
                <>
                  
                  <br />
                  <span className="subtitle"></span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Status Bar */}
      <div className="status-bar">
        <div className="status-section">
          <div className="connection-status">
            <div className={`status-dot ${isConnected ? 'connected' : 'disconnected'}`}></div>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          
          {systemStatus.model_loaded && (
            <div className="model-status">
              <div className="status-dot connected"></div>
              <span>Model Ready</span>
            </div>
          )}
          
          <div className="tts-status">
            <div className={`status-dot ${ttsStatus.available ? 'connected' : 'disconnected'}`}></div>
            <span>TTS {ttsStatus.available ? 'Ready' : 'Unavailable'}</span>
            {ttsStatus.is_speaking && <span className="speaking-indicator">🔊 Speaking</span>}
            {ttsStatus.queue_size > 0 && <span className="queue-indicator">Queue: {ttsStatus.queue_size}</span>}
            {ttsStatus.selected_voice && <span className="voice-indicator">Voice: {ttsStatus.selected_voice}</span>}
          </div>

          <div className="tts-auto-play-status">
            <div className="status-dot connected"></div>
            <span>Auto-Play: ON</span>
            <small>(susan/hazel)</small>
          </div>

          <div className="email-status">
            <div className={`status-dot ${systemStatus.email_service_available ? 'connected' : 'disconnected'}`}></div>
            <span>Email {systemStatus.email_service_available ? 'Active' : 'Inactive'}</span>
            {systemStatus.email_monitoring && <small>({systemStatus.email_monitoring})</small>}
          </div>
        </div>

        <div className="controls-section">
          <button onClick={clearHistory} className="control-btn">
            Clear History
          </button>

          {ttsStatus.available && (
            <div className="tts-controls">
              <button
                onClick={() => controlTTS('stop')}
                className="control-btn tts-btn"
                disabled={!ttsStatus.is_speaking}
                title="Stop current TTS playback"
              >
                🔇 Stop TTS
              </button>
              <button
                onClick={() => controlTTS('clear_queue')}
                className="control-btn tts-btn"
                disabled={ttsStatus.queue_size === 0}
                title="Clear TTS queue"
              >
                🗑️ Clear Queue
              </button>
            </div>
          )}

          {systemStatus.email_service_available && (
            <div className="email-controls">
              <button
                onClick={triggerEmailCheck}
                className="control-btn email-btn"
                title="Manually check for new emails"
              >
                📧 Check Email
              </button>
            </div>
          )}
        </div>
      </div>


      {/* Chat Container */}
      <div className="chat-container" ref={chatRef}>
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.role.toLowerCase()}`}>
            <div className="message-header">
              <strong className="role-label">{msg.role}:</strong>
              {msg.role === "Daeira" && (
                <div className="tts-controls-inline">
                  {msg.autoPlayed && (
                    <span className="auto-play-indicator" title="Auto-played with TTS">
                      🎵 Auto-played
                    </span>
                  )}
                  {ttsStatus.available && (
                    <button
                      className="speak-btn"
                      onClick={() => speakText(msg.mainContent, "normal")}
                      title="Speak this message again"
                    >
                      🔊 Replay
                    </button>
                  )}
                </div>
              )}
            </div>
            
            {/* Main message content */}
            <div className="message-content">
              {msg.role === "Daeira" ? msg.mainContent : msg.content}
              
              {/* Show image preview for user messages with images */}
              {msg.role === "Victor" && msg.imagePreview && (
                <div className="message-image">
                  <img src={msg.imagePreview} alt={msg.imageName || "Uploaded image"} className="message-img" />
                  <div className="image-info">📎 {msg.imageName}</div>
                </div>
              )}
            </div>

            {msg.ttsMessageId && (
              <div className="tts-info">
                <small>
                  TTS: {msg.ttsMessageId}
                  {msg.ttsPriority && ` (${msg.ttsPriority} priority)`}
                  {msg.autoPlayed && " - Auto-played"}
                </small>
              </div>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="loading-indicator">
            <div className="message daeira">
              <div className="message-header">
                <strong className="role-label">Daeira:</strong>
              </div>
              <div className="message-content">
                <div className="thinking-animation">
                  <span>●</span>
                  <span>●</span>
                  <span>●</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Input Section */}
      <div className="input-section">
        {/* Image Upload Area */}
        <div className="image-upload-container">
          <input 
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="image-upload-input"
            id="imageUpload"
            style={{ display: 'none' }}
          />
          <label htmlFor="imageUpload" className="image-upload-btn">
            📎 Add Image
          </label>
          
          {imageFile && (
            <div className="selected-image-info">
              <span className="image-name">{imageFile.name}</span>
              <button onClick={clearImage} className="clear-image-btn">✕</button>
            </div>
          )}
        </div>

        {/* Image Preview */}
        {imagePreview && (
          <div className="image-preview-container">
            <img src={imagePreview} alt="Preview" className="preview-img" />
          </div>
        )}

        <div className="input-container">
          <textarea
            placeholder="Type your message (enter to send, Ctrl+Enter for advanced mode)"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="message-input"
            disabled={isLoading}
          />
          <div className="input-buttons">
            <button 
              onClick={() => sendMessage(false)} 
              className="send-btn normal-btn"
              disabled={isLoading || (!input.trim() && !imageFile)}
            >
              SEND
            </button>
            <button 
              onClick={() => sendMessage(true)} 
              className="send-btn advanced-btn"
              disabled={isLoading || (!input.trim() && !imageFile)}
              title="Advanced mode with enhanced processing"
            >
              ADVANCED
            </button>
          </div>
        </div>
        
        <div className="input-help">
          <small>
            Enter: Send | Ctrl+Enter: Advanced Mode | 
            TTS: {ttsEnabled ? `Enabled (${ttsPriority})` : 'Disabled'}
            {imageFile && ` | Image: ${imageFile.name}`}
          </small>
        </div>
      </div>

      {/* New Section: Conversation History */}
      <div className="history-section">
        <button onClick={handleHistoryRetrieval} className="fetch-history-btn">
          Load Conversation History
        </button>
      </div>

            {/* TTS Controls Panel */}
      <div className="tts-panel">
        <div className="tts-controls">
          <label className="tts-toggle">
            <input
              type="checkbox"
              checked={ttsEnabled}
              onChange={(e) => setTtsEnabled(e.target.checked)}
            />
            <span>Enable TTS</span>
          </label>

          <select 
            value={ttsPriority} 
            onChange={(e) => setTtsPriority(e.target.value)}
            className="priority-select"
            disabled={!ttsEnabled}
          >
            <option value="low">Low Priority</option>
            <option value="normal">Normal Priority</option>
            <option value="high">High Priority</option>
            <option value="urgent">Urgent Priority</option>
          </select>

          <div className="tts-action-buttons">
            <button 
              onClick={() => controlTTS('stop')} 
              className="tts-btn stop-btn"
              disabled={!ttsStatus.available}
            >
              Stop
            </button>
            <button 
              onClick={() => controlTTS('clear_queue')} 
              className="tts-btn clear-btn"
              disabled={!ttsStatus.available}
            >
              Clear Queue
            </button>
          </div>
        </div>
      </div>


      <style jsx>{`
        .app {
          max-width: 768px;
          margin: 0 auto;
          min-height: 100vh;
          background: #f8f9fa;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .hero-section {
          position: relative;
          height: 312px;
          width: 768px;
          background-image: url(daeira-logo.png);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .hero-section:hover {
          filter: brightness(1.1);
        }

        .logo-container {
          position: relative;
          text-align: center;
          z-index: 2;
        }

        .logo-text {
          font-size: 4rem;
          font-weight: 900;
          color: white;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
          letter-spacing: 0.1em;
        }

        .overlay-content {
          position: absolute;
          width: 768px;
          height: 200px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          opacity: 0;
          transition: opacity 0.4s ease;
          background: rgba(0,0,0,0.25);
          padding: 2rem;
          border-radius: 20px;
          backdrop-filter: blur(5px);
          border: 1px solid rgba(255,255,255,0.1);
          min-width: 300px;
        }

        .overlay-content.visible {
          opacity: 1;
        }

        .overlay-text {
          color: white;
          font-size: 14px;
          font-family: 'Courier New', monospace;
          font-weight: 900;
          text-align: left;
          line-height: 1.4;
          max-height: 150px;
          overflow-y: auto;
        }

        .think-header {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #87ceeb;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }

        .think-body {
          font-size: 12px;
          font-weight: 400;
          line-height: 1.3;
          color: white;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
          background: rgba(0,0,0,0.2);
          padding: 8px;
          border-radius: 5px;
          border-left: 3px solid #87ceeb;
        }

        .subtitle {
          font-size: 12px;
          font-weight: 500;
        }

        .status-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          background: white;
          border-bottom: 1px solid #e9ecef;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .status-section {
          display: flex;
          align-items: center;
          gap: 1.5rem;
          flex-wrap: wrap;
        }

        .connection-status, .model-status, .tts-status {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.9rem;
          color: #6c757d;
        }

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }

        .status-dot.connected {
          background: #28a745;
        }

        .status-dot.disconnected {
          background: #dc3545;
        }

        .speaking-indicator {
          animation: pulse 1s infinite;
          margin-left: 0.5rem;
        }

        .queue-indicator {
          background: #007bff;
          color: white;
          padding: 0.2rem 0.4rem;
          border-radius: 10px;
          font-size: 0.7rem;
          margin-left: 0.5rem;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        .controls-section {
          display: flex;
          gap: 0.5rem;
          display: none; 
        }

        .control-btn {
          padding: 0.5rem 1rem;
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.9rem;
          transition: all 0.2s ease;
        }

        .control-btn:hover {
          background: #e9ecef;
        }

        .tts-panel {
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          padding: 1rem 1.5rem;
        }

        .tts-controls {
          display: flex;
          align-items: center;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .tts-toggle {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          font-size: 0.9rem;
        }

        .tts-toggle input {
          margin: 0;
        }

        .priority-select {
          padding: 0.3rem 0.5rem;
          border: 1px solid #ccc;
          border-radius: 4px;
          font-size: 0.9rem;
        }

        .priority-select:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .tts-action-buttons {
          display: flex;
          gap: 0.5rem;
        }

        .tts-btn {
          padding: 0.3rem 0.6rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.8rem;
          transition: all 0.2s ease;
        }

        .tts-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .stop-btn {
          background: #dc3545;
          color: white;
        }

        .stop-btn:hover:not(:disabled) {
          background: #c82333;
        }

        .clear-btn {
          background: #6c757d;
          color: white;
        }

        .clear-btn:hover:not(:disabled) {
          background: #5a6268;
        }

        .chat-container {
          height: 400px;
          background: white;
          overflow-y: auto;
          padding: 1rem;
          font-family: 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
        }

        .message {
          margin: 15px 0;
          padding: 10px;
          
          position: relative;
        }

        .message.victor {
          background:rgb(255, 255, 255);
          
        }

        .message.daeira {
          background:rgb(0, 0, 0);
          color: white;
    
        }

        .message.system {
          background:rgb(255, 0, 0);
        
          color:rgb(252, 252, 252);
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .role-label {
          font-weight: bold;
        }

        .speak-btn {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 1rem;
          opacity: 0.7;
          transition: opacity 0.2s ease;
        }

        .speak-btn:hover {
          opacity: 1;
        }

        /* NEW: Thought Process Section - Displayed ABOVE main content */
        .thought-process-section {
          background: rgba(0, 0, 0, 0.15);
          width: 768px;
          
          padding: 12px;
          margin-bottom: 12px;
          
        }

        .thought-header {
          font-weight: bold;
          font-size: 0.9rem;
          color: #ffc107;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .thought-content {
          font-size: 0.85rem;
          line-height: 1.4;
          color: rgba(255, 255, 255, 0.9);
          font-style: italic;
          white-space: pre-wrap;
        }

        .message-content {
          margin-top: 5px;
          word-wrap: break-word;
        }

        .tts-info {
          margin-top: 8px;
          opacity: 0.6;
        }

        .loading-indicator {
          margin: 10px 0;
        }

        .thinking-animation {
          display: flex;
          gap: 0.3rem;
        }

        .thinking-animation span {
          animation: bounce 1.4s infinite;
        }

        .thinking-animation span:nth-child(2) {
          animation-delay: 0.2s;
        }

        .thinking-animation span:nth-child(3) {
          animation-delay: 0.4s;
        }

        @keyframes bounce {
          0%, 60%, 100% { transform: translateY(0); }
          30% { transform: translateY(-10px); }
        }

        .input-section {
          padding: 1.5rem;
          background: white;
          border-top: 1px solid #e9ecef;
        }

        .input-container {
          display: flex;
          gap: 10px;
          align-items: flex-end;
        }

        .message-input {
          flex: 1;
          padding: 12px;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          min-height: 60px;
          max-height: 120px;
          resize: vertical;
          font-family: inherit;
          font-size: 14px;
          transition: border-color 0.2s ease;
        }

        .message-input:focus {
          outline: none;
          border-color: #007bff;
        }

        .message-input:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .input-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .send-btn {
          padding: 10px 16px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 600;
          font-size: 0.9rem;
          transition: all 0.2s ease;
          min-width: 80px;
        }

        .send-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .normal-btn {
          background: #007bff;
          color: white;
        }

        .normal-btn:hover:not(:disabled) {
          background: #0056b3;
        }

        .advanced-btn {
          background: #28a745;
          color: white;
        }

        .advanced-btn:hover:not(:disabled) {
          background: #1e7e34;
        }

        .input-help {
          margin-top: 0.5rem;
          text-align: center;
          color: #6c757d;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .status-bar {
            flex-direction: column;
            align-items: stretch;
          }

          .status-section {
            justify-content: center;
          }

          .tts-controls {
            justify-content: center;
          }

          .input-container {
            flex-direction: column;
          }

          .input-buttons {
            flex-direction: row;
            justify-content: stretch;
          }

          .send-btn {
            flex: 1;
          }

          .logo-text {
            font-size: 2rem;
          }

          .hero-section {
            height: 150px;
          }
        }
      `}</style>
    </div>
  );
};

export default App;