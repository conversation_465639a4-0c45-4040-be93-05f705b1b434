import base64
from io import BytesIO
import json
import os
import uuid
import mysql.connector
from mysql.connector import errorcode

import gradio as gr
import torch
from app_modules.gradio_utils import (
    cancel_outputing,
    delete_last_conversation,
    reset_state,
    reset_textbox,
    transfer_input,
    wrap_gen_fn,
)
from app_modules.overwrites import reload_javascript
from app_modules.presets import CONCURRENT_COUNT, description, description_top, title
from app_modules.utils import configure_logger, is_variable_assigned

from daeria_inference import (
    convert_conversation_to_prompts,
    deepseek_generate,
    load_model,
)
from utils.conversation import SeparatorStyle

db_config = {
    'user': 'daeira',
    'password': 'daeiraAssistant',
    'host': 'zenke.iad1-mysql-e2-7a.dreamhost.com',
    'database': 'daeira',
    'raise_on_warnings': True
}


class ConversationStorage:
    #def __init__(self, config):
        #self.db_config = config
        #self._initialize_database()

    #def _get_connection(self):
        #return mysql.connector.connect(**self.db_config)

    # def _initialize_database(self):
        # try:
            # temp_config = self.db_config.copy()
            # db_name = temp_config.pop('database')
            
            # cnx = mysql.connector.connect(**temp_config)
            # cursor = cnx.cursor()
            
            # cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} DEFAULT CHARACTER SET 'utf8mb4'")
            # cnx.database = db_name
            
            # table_query = """
            # CREATE TABLE IF NOT EXISTS conversation_history (
                # id INT AUTO_INCREMENT PRIMARY KEY,
                # session_id VARCHAR(36) NOT NULL,
                # role VARCHAR(20) NOT NULL,
                # message TEXT NOT NULL,
                # created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                # INDEX (session_id)
            # ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            # """
            # cursor.execute(table_query)
            # cnx.commit()
            # cursor.close()
            # cnx.close()
        # except mysql.connector.Error as err:
            # print(f"Failed to initialize database: {err}")
            # exit(1)

    def get_history(self, session_id):
        """Retrieves conversation history for a given session."""
        history = []
        try:
            with self._get_connection() as cnx:
                with cnx.cursor() as cursor:
                    query = "SELECT role, message FROM conversation_history WHERE session_id = %s ORDER BY created_at ASC"
                    cursor.execute(query, (session_id,))
                    history = [[role, msg] for (role, msg) in cursor]
        except mysql.connector.Error as err:
            print(f"Error fetching history: {err}")
        return history

    def save_turn(self, session_id, user_role, user_message, assistant_role, assistant_message):
        """Saves a single user-assistant turn to the database."""
        try:
            with self._get_connection() as cnx:
                with cnx.cursor() as cursor:
                    query = "INSERT INTO conversation_history (session_id, role, message) VALUES (%s, %s, %s)"
                    cursor.execute(query, (session_id, user_role, user_message))
                    cursor.execute(query, (session_id, assistant_role, assistant_message))
                    cnx.commit()
        except mysql.connector.Error as err:
            print(f"Error saving turn: {err}")
            
    def delete_last_turn(self, session_id):
        """Deletes the last two messages (one turn) for a session."""
        try:
            with self._get_connection() as cnx:
                with cnx.cursor() as cursor:
                    query_get_ids = "SELECT id FROM conversation_history WHERE session_id = %s ORDER BY id DESC LIMIT 2"
                    cursor.execute(query_get_ids, (session_id,))
                    ids_to_delete = [row[0] for row in cursor]
                    
                    if ids_to_delete:
                        placeholders = ', '.join(['%s'] * len(ids_to_delete))
                        query_delete = f"DELETE FROM conversation_history WHERE id IN ({placeholders})"
                        cursor.execute(query_delete, tuple(ids_to_delete))
                        cnx.commit()
        except mysql.connector.Error as err:
            print(f"Error deleting last turn: {err}")

    def clear_history(self, session_id):
        """Clears all history for a given session."""
        try:
            with self._get_connection() as cnx:
                with cnx.cursor() as cursor:
                    query = "DELETE FROM conversation_history WHERE session_id = %s"
                    cursor.execute(query, (session_id,))
                    cnx.commit()
        except mysql.connector.Error as err:
            print(f"Error clearing history: {err}")


def load_models():
    model_dir = "C:/DAEIRA/deepseek_R1_7B"
    return load_model(model_dir)


logger = configure_logger()
models = load_models()
conversation_storage = ConversationStorage(db_config)


def load_conversation_from_storage(session_id):
    return conversation_storage.get_history(session_id)


def get_prompt(conv) -> str:
    """
    Gets the prompt in the specified custom format (Victor/Daeira) with newlines.
    """
    ret = ""
    for role, message in conv.messages:
        if role == conv.roles[0]:
            ret += f"{role}: {message}\n"
        elif role == conv.roles[1]:
            if message:
                ret += f"{role}: {message}\n\n"
            else:
                ret += f"{role}:"
    return ret


def generate_prompt_with_history(text, session_id, vl_chat_processor, tokenizer, max_length=2048):
    history = load_conversation_from_storage(session_id)
    conversation = vl_chat_processor.new_chat_template()
    conversation.roles = ("Victor", "Daeira")
    
    # Dynamically attach our custom prompt function to the conversation object
    conversation.get_prompt = lambda: get_prompt(conversation)
    
    if history:
        processed_history = []
        for role, msg in history:
            if role.lower() in ['user', 'victor']:
                processed_history.append([conversation.roles[0], msg])
            elif role.lower() in ['assistant', 'daeira']:
                processed_history.append([conversation.roles[1], msg])
        conversation.messages = processed_history

    conversation.append_message(conversation.roles[0], text)
    conversation.append_message(conversation.roles[1], "")
    
    conversation_copy = conversation.copy()
    logger.info("=" * 80)
    logger.info(conversation.get_prompt())

    rounds = len(conversation.messages) // 2
    for _ in range(rounds):
        current_prompt = conversation.get_prompt()

        if torch.tensor(tokenizer.encode(current_prompt)).size(-1) <= max_length:
            return conversation_copy

        if len(conversation.messages) % 2 != 0:
            gr.Error("The messages between user and assistant are not paired.")
            return None

        try:
            for _ in range(2):
                conversation.messages.pop(0)
        except IndexError:
            gr.Error("Input text processing failed, unable to respond in this round.")
            return None

    gr.Error("Prompt could not be generated within max_length limit.")
    return None


def to_gradio_chatbot(conv):
    role_map = {conv.roles[0]: "Victor", conv.roles[1]: "Daeira"}
    messages = []
    for role, msg in conv.messages[conv.offset:]:
        if msg is not None:
            messages.append({"role": role_map.get(role, role.lower()), "content": msg})
    return messages


def to_gradio_history(conv):
    return conv.messages[conv.offset:]


@wrap_gen_fn
def generate_response(
    text,
    chatbot,
    history,
    top_p,
    temperature,
    repetition_penalty,
    max_length_tokens,
    max_context_length_tokens,
    session_id,
):
    try:
        tokenizer, vl_gpt, vl_chat_processor = models
        if text == "":
            yield chatbot, history, "Empty context."
            return
    except Exception:
        yield [[text, "No Model Found"]], [], "No Model Found"
        return

    conversation = generate_prompt_with_history(
        text, session_id, vl_chat_processor, tokenizer, max_length=max_context_length_tokens,
    )
    
    prompts = convert_conversation_to_prompts(conversation)
    gradio_chatbot_output = to_gradio_chatbot(conversation)

    full_response = ""
    with torch.no_grad():
        for x in deepseek_generate(
            prompts=prompts,
            model=vl_gpt,
            tokenizer=tokenizer,
            max_length=max_length_tokens,
            temperature=temperature,
            repetition_penalty=repetition_penalty,
            top_p=top_p,
        ):

            response = full_response
            conversation.update_last_message(response)
            if gradio_chatbot_output:
                gradio_chatbot_output[-1]["content"] = response
            yield gradio_chatbot_output, to_gradio_history(conversation), "Generating..."
    
    user_role, asst_role = conversation.roles
    final_response = conversation.messages[-1][-1]
    conversation_storage.save_turn(session_id, user_role, text, asst_role, final_response)

    print("flushed result to gradio")

    yield gradio_chatbot_output, to_gradio_history(conversation), "Generate: Success"


def retry(
    text,
    chatbot,
    history,
    top_p,
    temperature,
    repetition_penalty,
    max_length_tokens,
    max_context_length_tokens,
    session_id,
):
    if len(history) == 0:
        yield (chatbot, history, "Empty context")
        return

    conversation_storage.delete_last_turn(session_id)

    chatbot.pop()
    history.pop()
    last_user_turn = history.pop()
    text = last_user_turn[-1]
    
    yield from generate_response(
        text,
        chatbot,
        history,
        top_p,
        temperature,
        repetition_penalty,
        max_length_tokens,
        max_context_length_tokens,
        session_id,
    )


def build_daeira(models):
    with open("assets/custom.css", "r", encoding="utf-8") as f:
        customCSS = f.read()

    with gr.Blocks(theme=gr.themes.Soft(), css=customCSS) as daeira:
        session_id = gr.State(lambda: str(uuid.uuid4()))
        history = gr.State([])
        input_text = gr.State()

        with gr.Row():
            gr.HTML(title)
            status_display = gr.Markdown("Success", elem_id="status_display")
        gr.Markdown(description_top)

        with gr.Row(equal_height=True):
            with gr.Column(scale=4):
                with gr.Row():
                    chatbot = gr.Chatbot(
                        elem_id="daeira", show_share_button=True, type="messages", height=600,
                    )
                with gr.Row():
                    with gr.Column(scale=4):
                        text_box = gr.Textbox(
                            show_label=False, placeholder="Enter text", container=False
                        )
                    with gr.Column(min_width=70):
                        submitBtn = gr.Button("Send")
                    with gr.Column(min_width=70):
                        cancelBtn = gr.Button("Stop")
                with gr.Row():
                    emptyBtn = gr.Button("New Conversation")
                    retryBtn = gr.Button("Regenerate")
                    delLastBtn = gr.Button("Remove Last Turn")

            with gr.Column():
                with gr.Tab(label="Parameter Setting"):
                    top_p = gr.Slider(minimum=0, maximum=1.0, value=0.95, step=0.05, interactive=True, label="Top-p")
                    temperature = gr.Slider(minimum=0, maximum=1.0, value=0.1, step=0.1, interactive=True, label="Temperature")
                    repetition_penalty = gr.Slider(minimum=0.0, maximum=2.0, value=1.1, step=0.1, interactive=True, label="Repetition penalty")
                    max_length_tokens = gr.Slider(minimum=0, maximum=131072, value=2048, step=8, interactive=True, label="Max Generation Tokens")
                    max_context_length_tokens = gr.Slider(minimum=0, maximum=131072, value=4096, step=128, interactive=True, label="Max History Tokens")

        input_widgets = [
            input_text, chatbot, history, top_p, temperature, repetition_penalty,
            max_length_tokens, max_context_length_tokens, session_id,
        ]
        output_widgets = [chatbot, history, status_display]
        
        transfer_input_args = dict(fn=transfer_input, inputs=[text_box], outputs=[input_text, text_box, submitBtn], show_progress=True)
        predict_args = dict(fn=generate_response, inputs=input_widgets, outputs=output_widgets, show_progress=True)
        retry_args = dict(fn=retry, inputs=input_widgets, outputs=output_widgets, show_progress=True)
        
        predict_events = [
            text_box.submit(**transfer_input_args).then(**predict_args),
            submitBtn.click(**transfer_input_args).then(**predict_args),
        ]
        retryBtn.click(**retry_args)
        
        def clear_and_reset_state(sid):
            conversation_storage.clear_history(sid)
            return ([], [], "New conversation started.")

        emptyBtn.click(clear_and_reset_state, inputs=[session_id], outputs=output_widgets, show_progress=True)
        emptyBtn.click(reset_textbox, [], [text_box])

        def remove_last_turn_and_update_ui(chat, hist, sid):
            conversation_storage.delete_last_turn(sid)
            return delete_last_conversation(chat, hist)
        
        delLastBtn.click(remove_last_turn_and_update_ui, [chatbot, history, session_id], output_widgets, show_progress=True)
        cancelBtn.click(cancel_outputing, [], [status_display], cancels=predict_events)

    return daeira


if __name__ == "__main__":
    daeira = build_daeira(models)
    daeira.title = "DAEIRA"
    reload_javascript()
    daeira.queue().launch(
        share=False,
        favicon_path="assets/favicon.ico",
        inbrowser=False,
        server_name="127.0.0.1",
        server_port=8000,
    )