import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

# Third-party imports
import mysql.connector
import chromadb
from sentence_transformers import SentenceTransformer

# Assuming InputObject is defined in a sibling 'APP' directory
# This import path may need to be adjusted based on your project structure.
from APP.daeira_input import InputObject

# --- Module-Level Configuration & Setup ---
logger = logging.getLogger(__name__)

# Module-level clients will be initialized by initialize_persistence_services()
# This avoids global state at import time and ensures explicit initialization.
EMBEDDING_MODEL = None
VECTOR_DB_CLIENT = None
VECTOR_COLLECTION = None

# --- Configuration Function ---

def get_config() -> Dict[str, Any]:
    """
    Single, hardcoded source of truth for all persistence configurations.
    This is a deliberate defensive strategy to avoid environmental attack vectors.
    """
    return {
        "sql_db": {
            "host": "localhost",
            "port": 3306,
            "user": "daeira",
            "password": "daeira",
            "database": "daeira",
            "auth_plugin": "mysql_native_password"
        },
        "vector_db": {
            "persistence_path": "C:/DAEIRA/MEMORY",
            "collection_name": "daeira_semantic_knowledge",
            "embedding_model_name": "all-MiniLM-L6-v2"
        }
    }

# --- Initialization Functions ---

def initialize_persistence_services():
    """
    Initializes all persistence services (SQL schema, Vector DB client).
    This function must be called once at application startup.
    """
    global EMBEDDING_MODEL, VECTOR_DB_CLIENT, VECTOR_COLLECTION

    logger.info("Initializing persistence services...")
    config = get_config()

    # 1. Initialize SQL Database Schema
    try:
        initialize_database_schema(config['sql_db'])
        logger.info("SQL database schema verified/created successfully.")
    except mysql.connector.Error as err:
        logger.critical(f"FATAL: Failed to initialize SQL database schema: {err}", exc_info=True)
        raise

    # 2. Initialize Sentence Transformer Model
    try:
        model_name = config['vector_db']['embedding_model_name']
        logger.info(f"Loading embedding model: {model_name}...")
        EMBEDDING_MODEL = SentenceTransformer(model_name)
        logger.info("Embedding model loaded successfully.")
    except Exception as e:
        logger.critical(f"FATAL: Failed to load embedding model: {e}", exc_info=True)
        raise

    # 3. Initialize Vector Database Client and Collection
    try:
        path = config['vector_db']['persistence_path']
        collection_name = config['vector_db']['collection_name']
        logger.info(f"Initializing vector database at: {path}")
        VECTOR_DB_CLIENT = chromadb.PersistentClient(path=path)
        VECTOR_COLLECTION = VECTOR_DB_CLIENT.get_or_create_collection(name=collection_name)
        logger.info(f"Vector database collection '{collection_name}' is ready.")
    except Exception as e:
        logger.critical(f"FATAL: Failed to initialize vector database: {e}", exc_info=True)
        raise

    logger.info("All persistence services initialized successfully.")

def initialize_database_schema(sql_config: Dict[str, Any]):
    """
    Connects to the database and ensures the required table schema exists.
    This is a robust, trusted function.
    """
    connection = None
    try:
        connection = mysql.connector.connect(**sql_config)
        cursor = connection.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS InputObjects (
            event_id VARCHAR(36) PRIMARY KEY,
            timestamp DATETIME(6) NOT NULL,
            source_type VARCHAR(255) NOT NULL,
            source_details JSON NOT NULL,
            content_type VARCHAR(255) NOT NULL,
            content_payload LONGTEXT NOT NULL,
            priority INT NULL,
            initial_tags JSON NULL,
            processed_by_evaluate_timestamp DATETIME(6) NULL,
            evaluation_outcome_summary TEXT NULL,
            linked_event_ids JSON NULL,
            INDEX idx_timestamp (timestamp),
            INDEX idx_source_type (source_type),
            INDEX idx_priority (priority),
            INDEX idx_processed_timestamp (processed_by_evaluate_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        connection.commit()
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

# --- SQL Persistence Functions ---

def sql_store_object(input_obj: InputObject):
    """Saves a single InputObject to the SQL database."""
    # This function will open, use, and close its own connection.
    # To be implemented.
    pass

def sql_get_object_by_id(event_id: str) -> Optional[InputObject]:
    """Retrieves a single InputObject from SQL by its ID."""
    # To be implemented.
    pass

def sql_update_evaluated_object(event_id: str, summary: str, linked_ids: List[str]):
    """Updates an object in SQL after the evaluation stage."""
    # To be implemented.
    pass

# --- Vector Persistence Functions ---

def vector_add_embedding(input_obj: InputObject):
    """Creates and stores an embedding for an InputObject in the vector database."""
    # To be implemented.
    pass

def vector_search(query_text: str, top_k: int = 5) -> List[str]:
    """Performs a semantic search and returns a list of text strings."""
    # To be implemented.
    pass


# --- Standalone Test Block ---

if __name__ == "__main__":
    # This block is for direct testing of this module's functions.
    # It provides a safe, isolated environment for diagnostics.
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    logger.info("--- [START] Standalone Test for daeira_memory.py ---")
    
    try:
        # 1. Initialize all services
        initialize_persistence_services()
        
        # 2. Define a test object
        test_event_id = "test-standalone-12345"
        test_object = InputObject(
            event_id=test_event_id,
            source_type="standalone_test",
            content_type="text/plain",
            content="This is a test of the new, simplified persistence layer."
        )
        
        logger.info(f"Test Object Created: ID = {test_event_id}")

        # --- Test logic will be added here as functions are implemented ---
        # Example:
        # logger.info("Testing SQL storage...")
        # sql_store_object(test_object)
        # retrieved = sql_get_object_by_id(test_event_id)
        # if retrieved and retrieved.event_id == test_event_id:
        #     logger.info("SUCCESS: SQL store/retrieve test passed.")
        # else:
        #     logger.error("FAILURE: SQL store/retrieve test failed.")

        logger.info("Standalone script logic placeholder. Implement tests as functions are built.")

    except Exception as e:
        logger.critical(f"A critical error occurred during the standalone test: {e}", exc_info=True)
    
    logger.info("--- [END] Standalone Test for daeira_memory.py ---")