import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

# Third-party imports
# Environment variables removed - using hardcoded configuration
import mysql.connector
from mysql.connector import pooling
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from pydantic import BaseModel, Field
from input import InputObject


logger = logging.getLogger(__name__)



def get_db_config() -> Dict[str, Any]:
    """
    Single source of truth for database configuration.
    All database connections should use this function.
    """
    return {
        "host": "localhost",
        "port": 3306,
        "user": "daeira",
        "password": "daeira",
        "database": "daeira",
        "auth_plugin": "mysql_native_password"
    }

def initialize_database(config: Dict[str, Any]):
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS InputObjects (
            event_id VARCHAR(36) PRIMARY KEY,
            timestamp DATETIME(6) NOT NULL,
            source_type VARCHAR(255) NOT NULL,
            source_details JSON NOT NULL,
            content_type VARCHAR(255) NOT NULL,
            content_payload LONGTEXT NOT NULL,
            priority INT NULL,
            initial_tags JSON NULL,
            processed_by_evaluate_timestamp DATETIME(6) NULL,
            evaluation_outcome_summary TEXT NULL,
            linked_event_ids JSON NULL,
            INDEX idx_timestamp (timestamp),
            INDEX idx_source_type (source_type),
            INDEX idx_priority (priority),
            INDEX idx_processed_timestamp (processed_by_evaluate_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        connection.commit()
        logger.info("Database schema verified/created successfully.")
    except mysql.connector.Error as err:
        logger.error(f"FATAL: Failed to initialize database: {err}")
        raise
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()


class ContextItem(BaseModel):
    """
    Standardized structure for context items returned during RAG operations.
    Used by the meta-cognitive layer for prompt construction.
    """
    content: str  # The actual text content
    source_type: str  # Where this content originated from
    relevance_score: float  # How relevant is this to the current query (0.0-1.0)
    timestamp: datetime  # When was this content created/stored
    author: Optional[str] = None  # Who created this content, if applicable
    title: Optional[str] = None  # Title or subject if applicable
    doc_id: str  # Unique identifier linking to the original source
    metadata: Dict[str, Any]  # Additional metadata that might be useful


class MemoryStore:
    """
    Unified memory store handling both structured (MySQL) and vector (ChromaDB) operations.
    Eliminates redundancy by consolidating all memory operations into a single class.
    """

    def __init__(self, config: Dict[str, Any]):
        logger.info("MemoryStore: initializing unified memory system...")

        # Initialize MySQL connection pool
        try:
            mysql_config = config.get("mysql", {})
            self.pool = pooling.MySQLConnectionPool(
                pool_name="daeira_pool",
                pool_size=5,
                **mysql_config
            )
            logger.info("Database connection pool established successfully.")
        except mysql.connector.Error as err:
            logger.error(f"FATAL: Failed to create database connection pool: {err}")
            raise

        # Initialize ChromaDB vector store
        try:
            self.persistence_path = "C:\\DAEIRA\\MEMORY"
            self.collection_name = "daeira_knowledge"
            self.embedding_model_name = "all-MiniLM-L6-v2"

            logger.info(f"MemoryStore: initializing SentenceTransformer ({self.embedding_model_name})")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)

            self.client = chromadb.PersistentClient(path=self.persistence_path)
            self.collection = self.client.get_or_create_collection(name=self.collection_name)

            logger.info(f"Vector store initialized with collection '{self.collection_name}'")
        except Exception as err:
            logger.error(f"FATAL: Failed to initialize vector store: {err}")
            raise

        logger.info("MemoryStore: unified memory system initialized successfully")

    def get_cursor(self, dictionary: bool = False):
        """
        Context manager for MySQL cursor.
        Usage:
            with self.get_cursor() as cursor:
                cursor.execute(...)
        """
        from contextlib import contextmanager

        @contextmanager
        def _cursor_context():
            connection = self.pool.get_connection()
            cursor = connection.cursor(dictionary=dictionary)
            try:
                yield cursor
                connection.commit()
            except mysql.connector.Error as err:
                connection.rollback()
                logger.error(f"Database transaction failed: {err}")
                raise
            finally:
                cursor.close()
                connection.close()  # Returns the connection to the pool

        return _cursor_context()

    def row_to_input_object(self, row: Dict[str, Any]) -> 'InputObject':

        for key in ['source_details', 'initial_tags', 'linked_event_ids']:
            if isinstance(row.get(key), str):
                row[key] = json.loads(row[key])


        if row.get('content_type', '').startswith('application/json') and isinstance(row.get('content_payload'), str):
            row['content_payload'] = json.loads(row['content_payload'])

        init_args = {
            'event_id': row.get('event_id'),
            'timestamp': row.get('timestamp'),
            'source_type': row.get('source_type'),
            'source_details': row.get('source_details'),
            'content_type': row.get('content_type'),
            'content': row.get('content_payload'), # Map db 'content_payload' to 'content'
            'priority': row.get('priority'),
            'initial_tags': row.get('initial_tags')
        }
        return InputObject(**{k: v for k, v in init_args.items() if v is not None})


    def store_input_object(self, input_obj: 'InputObject') -> str:
        """Store InputObject in both structured and vector stores"""
        # Store in structured store (MySQL)
        sql = """
            INSERT INTO InputObjects (
                event_id, timestamp, source_type, source_details,
                content_type, content_payload, priority, initial_tags
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        content_payload = json.dumps(input_obj.content) if isinstance(input_obj.content, dict) else input_obj.content
        params = (
            input_obj.event_id, input_obj.timestamp, input_obj.source_type,
            json.dumps(input_obj.source_details), input_obj.content_type,
            content_payload, input_obj.priority,
            json.dumps(input_obj.initial_tags) if input_obj.initial_tags else None
        )

        with self.get_cursor() as cursor:
            cursor.execute(sql, params)

        # Store in vector store if content is suitable for embedding
        if isinstance(input_obj.content, str) and input_obj.content_type in [
            'text/plain', 'text/markdown', 'text/html', 'application/json'
        ]:
            self._add_to_vector_store(input_obj)

        logger.info(f"Stored InputObject with event_id: {input_obj.event_id}")
        return input_obj.event_id

    def get_input_object_by_id(self, event_id: str) -> Optional['InputObject']:
        """Retrieves an InputObject by its event_id."""
        with self.get_cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM InputObjects WHERE event_id = %s", (event_id,))
            result = cursor.fetchone()
        
        return self.row_to_input_object(result) if result else None

    def get_recent_input_objects(self, limit: int = 10, **filters) -> List['InputObject']:
        """Retrieves recent InputObjects with optional filtering."""
        query = "SELECT * FROM InputObjects"
        conditions = []
        params = []

        # Safely build WHERE clause
        for key, value in filters.items():
            if value is not None:
                # Basic whitelist to prevent injection on column names
                if key in ['source_type', 'before_timestamp']:
                    operator = '<' if key == 'before_timestamp' else '='
                    conditions.append(f"{key.replace('before_', '')} {operator} %s")
                    params.append(value)
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
            
        query += " ORDER BY timestamp DESC LIMIT %s"
        params.append(limit)
        
        with self.get_cursor(dictionary=True) as cursor:
            cursor.execute(query, tuple(params))
            results = cursor.fetchall()

        return [self.row_to_input_object(row) for row in results]

    def update_input_object_post_evaluation(self, event_id: str, outcome_summary: str, linked_ids: Optional[List[str]] = None):
        """Updates an InputObject after evaluation."""
        sql = """
            UPDATE InputObjects SET
                processed_by_evaluate_timestamp = %s,
                evaluation_outcome_summary = %s,
                linked_event_ids = %s
            WHERE event_id = %s
        """
        params = (
            datetime.now(timezone.utc),
            outcome_summary,
            json.dumps(linked_ids) if linked_ids else None,
            event_id
        )
        
        with self.get_cursor() as cursor:
            cursor.execute(sql, params)
        
        logger.info(f"Updated InputObject post-evaluation: {event_id}")

    def _sanitize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize metadata for ChromaDB storage"""
        sanitized = {}
        for key, value in metadata.items():
            if isinstance(value, list):
                sanitized[key] = ', '.join(str(item) for item in value) if value else ""
            elif isinstance(value, dict):
                sanitized[key] = json.dumps(value)
            elif value is None:
                sanitized[key] = None
            elif isinstance(value, (str, int, float, bool)):
                sanitized[key] = value
            else:
                sanitized[key] = str(value)
        return sanitized

    def _add_to_vector_store(self, input_obj: 'InputObject'):
        """Add InputObject to vector store"""
        try:
            # Prepare vector metadata
            vector_metadata = {
                "parent_event_id": input_obj.event_id,
                "source_type": input_obj.source_type,
                "timestamp": input_obj.timestamp.isoformat(),
                "content_type": input_obj.content_type,
                "tags": input_obj.initial_tags if input_obj.initial_tags else []
            }

            # Add source details to metadata
            for key, value in input_obj.source_details.items():
                if isinstance(value, (str, int, float, bool)):
                    vector_metadata[f"source_{key}"] = value

            # Add content preview
            content_text = input_obj.content
            if isinstance(content_text, dict):
                content_text = json.dumps(content_text)
            preview_length = min(200, len(content_text))
            vector_metadata["content_preview"] = content_text[:preview_length]

            # Sanitize metadata and add to vector store
            sanitized_metadata = self._sanitize_metadata(vector_metadata)
            embedding = self.embedding_model.encode(content_text).tolist()

            self.collection.add(
                embeddings=[embedding],
                documents=[content_text],
                ids=[input_obj.event_id],
                metadatas=[sanitized_metadata]
            )

        except Exception as e:
            logger.error(f"Failed to add to vector store: {e}")

    def semantic_search(self, query_text: str, top_k: int = 5, filters: Optional[Dict] = None) -> List[Dict]:
        """Perform semantic search in vector store"""
        try:
            query_embedding = self.embedding_model.encode(query_text).tolist()

            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=filters
            )

            processed_results = []
            if results['documents'] and len(results['documents']) > 0:
                for i, doc in enumerate(results['documents'][0]):
                    processed_results.append({
                        'document': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'id': results['ids'][0][i],
                        'distance': results['distances'][0][i] if 'distances' in results else None
                    })

            return processed_results
        except Exception as e:
            logger.error(f"Failed to perform semantic search: {e}")
            raise

    def retrieve_context_for_task(
        self,
        current_task_description: str,
        conversation_history: List[InputObject],
        context_config: Optional[Dict] = None
    ) -> List[ContextItem]:
        """
        Retrieve relevant context for a task, combining semantic search results
        and recent conversation history.
        """
        if context_config is None:
            context_config = {}

        # Set defaults for context_config
        semantic_results_count = context_config.get("semantic_results_count", 5)
        history_inclusion_count = context_config.get("history_inclusion_count", 3)
        metadata_filters = context_config.get("metadata_filters", None)

        # 1. Perform semantic search using the task description
        semantic_results = self.semantic_search(
            query_text=current_task_description,
            top_k=semantic_results_count,
            filters=metadata_filters
        )

        # 2. Process conversation history
        history_items = []
        for item in conversation_history[-history_inclusion_count:]:
            if isinstance(item.content, dict):
                content_text = json.dumps(item.content)
            else:
                content_text = item.content

            history_items.append({
                "content": content_text,
                "event_id": item.event_id,
                "timestamp": item.timestamp,
                "source_type": item.source_type,
                "metadata": item.source_details
            })

        # 3. Combine and prioritize results
        combined_results = []

        # Add semantic search results
        for result in semantic_results:
            relevance_score = 1.0 - (result.get('distance', 0) / 2.0) if result.get('distance') is not None else 0.9

            metadata = result.get('metadata', {})
            timestamp_str = metadata.get('timestamp')
            timestamp = datetime.fromisoformat(timestamp_str) if timestamp_str else datetime.now(timezone.utc)

            context_item = ContextItem(
                content=result['document'],
                source_type=metadata.get('source_type', 'unknown'),
                relevance_score=relevance_score,
                timestamp=timestamp,
                author=metadata.get('source_from', None),
                title=metadata.get('source_subject_or_title', None),
                doc_id=result['id'],
                metadata=metadata
            )
            combined_results.append(context_item)

        # Add conversation history
        for item in history_items:
            metadata = item.get('metadata', {})

            context_item = ContextItem(
                content=item['content'],
                source_type=item['source_type'],
                relevance_score=1.0,  # History items are highly relevant by default
                timestamp=item['timestamp'],
                author=metadata.get('from', None),
                title=metadata.get('subject', None),
                doc_id=item['event_id'],
                metadata=metadata
            )
            combined_results.append(context_item)

        # Sort by relevance and recency
        combined_results.sort(key=lambda x: (x.relevance_score, x.timestamp), reverse=True)

        return combined_results


class ContextItem(BaseModel):
    content: str  # The actual text content
    source_type: str  # Where this content originated from
    relevance_score: float  # How relevant is this to the current query (0.0-1.0)
    timestamp: datetime  # When was this content created/stored
    author: Optional[str] = None  # Who created this content, if applicable
    title: Optional[str] = None  # Title or subject if applicable
    doc_id: str  # Unique identifier linking to the original source
    metadata: Dict[str, Any]  # Additional metadata that might be useful


MemoryManagerDaeira = MemoryStore


def initialize_memory():
    """
    Initialize the unified memory system using centralized configuration.
    """
    # Use centralized database configuration
    db_config = get_db_config()

    # Configure memory system using single source of truth
    memory_config = {
        "mysql": db_config,
        "chromadb": {
            "persistence_path": "C:/DAEIRA/MEMORY",
            "collection_name": "daeira_knowledge_base",
            "embedding_model": "all-MiniLM-L6-v2"
        }
    }

    # Validate required configuration
    if not memory_config["mysql"]["password"]:
        raise ValueError("Database password is required")

    # Initialize database schema
    initialize_database(db_config)

    # Create unified memory store
    memory_store = MemoryStore(memory_config)
    return memory_store


if __name__ == "__main__":
    # For testing or standalone execution
    logging.basicConfig(level=logging.INFO)
    memory_store = initialize_memory()
    logger.info("MEMORY_daeira.py initialized and ready.")
