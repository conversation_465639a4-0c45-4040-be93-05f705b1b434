import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

# Third-party imports
import mysql.connector
import chromadb
from sentence_transformers import SentenceTransformer

# Assuming InputObject is defined in a sibling 'APP' directory
# This import path may need to be adjusted based on your project structure.
from daeira_input import InputObject



# Module-level clients will be initialized by their respective functions.
# This avoids global state at import time and ensures explicit initialization.
EMBEDDING_MODEL = None
VECTOR_DB_CLIENT = None
VECTOR_COLLECTION = None

# --- Configuration Function ---

def get_config() -> Dict[str, Any]:
    """
    Single, hardcoded source of truth for all persistence configurations.
    This is a deliberate defensive strategy to avoid environmental attack vectors.
    """
    return {
        "sql_db": {
            "host": "localhost",
            "port": 3306,
            "user": "daeira",
            "password": "daeira",
            "database": "daeira",
            "auth_plugin": "mysql_native_password"
        },
        "vector_db": {
            "persistence_path": "C:/DAEIRA/MEMORY",
            "collection_name": "daeira_semantic_knowledge",
            "embedding_model_name": "all-MiniLM-L6-v2"
        }
    }

# --- Initialization Functions ---

def initialize_sql_schema():
    """
    Connects to the database and ensures the required table schema exists.
    This is a robust, trusted function. Must be called at startup.
    """
    sql_config = get_config()['sql_db']
    connection = None
    try:
        connection = mysql.connector.connect(**sql_config)
        cursor = connection.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS InputObjects (
            event_id VARCHAR(36) PRIMARY KEY,
            timestamp DATETIME(6) NOT NULL,
            source_type VARCHAR(255) NOT NULL,
            source_details JSON NOT NULL,
            content_type VARCHAR(255) NOT NULL,
            content_payload LONGTEXT NOT NULL,
            priority INT NULL,
            initial_tags JSON NULL,
            processed_by_evaluate_timestamp DATETIME(6) NULL,
            evaluation_outcome_summary TEXT NULL,
            linked_event_ids JSON NULL,
            INDEX idx_timestamp (timestamp),
            INDEX idx_source_type (source_type),
            INDEX idx_priority (priority),
            INDEX idx_processed_timestamp (processed_by_evaluate_timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """)
        connection.commit()
        logger.info("SQL database schema verified/created successfully.")
    except mysql.connector.Error as err:
        logger.critical(f"FATAL: Failed to initialize SQL database schema: {err}", exc_info=True)
        raise
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def initialize_vector_db():
    """
    Initializes the embedding model and vector database client.
    Must be called once at application startup.
    """
    global EMBEDDING_MODEL, VECTOR_DB_CLIENT, VECTOR_COLLECTION
    config = get_config()['vector_db']

    # 1. Initialize Sentence Transformer Model
    try:
        model_name = config['embedding_model_name']
        logger.info(f"Loading embedding model: {model_name}...")
        EMBEDDING_MODEL = SentenceTransformer(model_name)
        logger.info("Embedding model loaded successfully.")
    except Exception as e:
        logger.critical(f"FATAL: Failed to load embedding model: {e}", exc_info=True)
        raise

    # 2. Initialize Vector Database Client and Collection
    try:
        path = config['persistence_path']
        collection_name = config['collection_name']
        logger.info(f"Initializing vector database at: {path}")
        VECTOR_DB_CLIENT = chromadb.PersistentClient(path=path)
        VECTOR_COLLECTION = VECTOR_DB_CLIENT.get_or_create_collection(name=collection_name)
        logger.info(f"Vector database collection '{collection_name}' is ready.")
    except Exception as e:
        logger.critical(f"FATAL: Failed to initialize vector database: {e}", exc_info=True)
        raise

# --- SQL Helper Function ---

def _sql_row_to_input_object(row: Dict[str, Any]) -> InputObject:
    """Converts a dictionary from a SQL row into a structured InputObject."""
    # Handle potential JSON strings that need to be loaded
    for key in ['source_details', 'initial_tags', 'linked_event_ids']:
        if isinstance(row.get(key), str):
            try:
                row[key] = json.loads(row[key])
            except json.JSONDecodeError:
                logger.warning(f"Could not decode JSON for key '{key}' in event_id '{row.get('event_id')}'.")
                row[key] = None

    # Handle the content payload which could also be a JSON string
    if row.get('content_type', '').startswith('application/json') and isinstance(row.get('content_payload'), str):
        try:
            row['content'] = json.loads(row['content_payload'])
        except json.JSONDecodeError:
            logger.warning(f"Could not decode JSON content_payload for event_id '{row.get('event_id')}'.")
            row['content'] = row['content_payload'] # Fallback to raw string
    else:
        row['content'] = row.get('content_payload')

    # Construct the InputObject, filtering out keys that don't belong
    # and mapping 'content_payload' to 'content'.
    input_obj_keys = InputObject.__fields__.keys()
    init_args = {k: v for k, v in row.items() if k in input_obj_keys or k == 'content'}
    
    return InputObject(**init_args)

# --- SQL Persistence Functions ---

def sql_store_object(input_obj: InputObject):
    """Saves a single InputObject to the SQL database."""
    sql_config = get_config()['sql_db']
    connection = None
    try:
        connection = mysql.connector.connect(**sql_config)
        cursor = connection.cursor()
        
        # Serialize complex fields to JSON strings for storage
        content_payload = json.dumps(input_obj.content) if isinstance(input_obj.content, dict) else input_obj.content
        source_details_json = json.dumps(input_obj.source_details)
        initial_tags_json = json.dumps(input_obj.initial_tags) if input_obj.initial_tags else None
        
        sql = """
            INSERT INTO InputObjects (
                event_id, timestamp, source_type, source_details,
                content_type, content_payload, priority, initial_tags
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            input_obj.event_id, input_obj.timestamp, input_obj.source_type,
            source_details_json, input_obj.content_type, content_payload,
            input_obj.priority, initial_tags_json
        )
        
        cursor.execute(sql, params)
        connection.commit()
        logger.debug(f"SQL: Stored InputObject with event_id: {input_obj.event_id}")

    except mysql.connector.Error as err:
        logger.error(f"SQL store failed for event_id {input_obj.event_id}: {err}", exc_info=True)
        if connection:
            connection.rollback()
        raise
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()


def sql_get_object_by_id(event_id: str) -> Optional[InputObject]:
    """Retrieves a single InputObject from SQL by its ID."""
    sql_config = get_config()['sql_db']
    connection = None
    try:
        connection = mysql.connector.connect(**sql_config)
        cursor = connection.cursor(dictionary=True) # Fetch rows as dictionaries
        
        cursor.execute("SELECT * FROM InputObjects WHERE event_id = %s", (event_id,))
        row = cursor.fetchone()
        
        if row:
            return _sql_row_to_input_object(row)
        return None

    except mysql.connector.Error as err:
        logger.error(f"SQL get failed for event_id {event_id}: {err}", exc_info=True)
        raise
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()


def sql_update_evaluated_object(event_id: str, summary: str, linked_ids: Optional[List[str]] = None):
    """Updates an object in SQL after the evaluation stage."""
    sql_config = get_config()['sql_db']
    connection = None
    try:
        connection = mysql.connector.connect(**sql_config)
        cursor = connection.cursor()

        sql = """
            UPDATE InputObjects SET
                evaluation_outcome_summary = %s,
                linked_event_ids = %s,
                processed_by_evaluate_timestamp = %s
            WHERE event_id = %s
        """
        params = (
            summary,
            json.dumps(linked_ids) if linked_ids else None,
            datetime.now(timezone.utc),
            event_id
        )

        cursor.execute(sql, params)
        connection.commit()
        logger.debug(f"SQL: Updated evaluated object: {event_id}")

    except mysql.connector.Error as err:
        logger.error(f"SQL update failed for event_id {event_id}: {err}", exc_info=True)
        if connection:
            connection.rollback()
        raise
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()


# --- Vector Persistence Functions ---

def vector_add_embedding(input_obj: InputObject):
    """Creates and stores an embedding for an InputObject in the vector database."""
    if not VECTOR_COLLECTION or not EMBEDDING_MODEL:
        logger.error("Vector DB not initialized. Cannot add embedding.")
        return

    # Only embed string content
    if not isinstance(input_obj.content, str):
        logger.debug(f"Skipping vector embedding for non-string content (event_id: {input_obj.event_id})")
        return

    try:
        embedding = EMBEDDING_MODEL.encode(input_obj.content).tolist()
        
        # Prepare metadata, ensuring all values are simple types
        metadata = {
            "source_type": input_obj.source_type,
            "timestamp_iso": input_obj.timestamp.isoformat(),
            "content_type": input_obj.content_type,
            # Add other simple details from source_details if needed
        }

        VECTOR_COLLECTION.add(
            embeddings=[embedding],
            documents=[input_obj.content],
            metadatas=[metadata],
            ids=[input_obj.event_id]
        )
        logger.debug(f"Vector: Added embedding for event_id: {input_obj.event_id}")
    except Exception as e:
        logger.error(f"Vector add embedding failed for event_id {input_obj.event_id}: {e}", exc_info=True)
        # Do not re-raise to avoid halting execution for a non-critical vector error
        

def vector_search(query_text: str, top_k: int = 5) -> List[str]:
    """Performs a semantic search and returns a list of text strings."""
    if not VECTOR_COLLECTION or not EMBEDDING_MODEL:
        logger.error("Vector DB not initialized. Cannot perform search.")
        return []

    try:
        query_embedding = EMBEDDING_MODEL.encode(query_text).tolist()
        
        results = VECTOR_COLLECTION.query(
            query_embeddings=[query_embedding],
            n_results=top_k
        )
        
        # Return the list of document strings, which is the most direct, useful result
        return results['documents'][0] if results and results['documents'] else []
        
    except Exception as e:
        logger.error(f"Vector search failed for query '{query_text[:50]}...': {e}", exc_info=True)
        return []


# --- Standalone Test Block ---

if __name__ == "__main__":
    # This block is for direct testing of this module's functions.
    # It provides a safe, isolated environment for diagnostics.
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    logger.info("--- [START] Standalone Test for daeira_memory.py ---")
    
    try:
        # 1. Initialize all services separately
        initialize_sql_schema()
        initialize_vector_db()
        
        # 2. Define a test object
        test_event_id = f"test-standalone-{uuid.uuid4()}"
        test_object = InputObject(
            event_id=test_event_id,
            source_type="standalone_test",
            content_type="text/plain",
            source_details={"user": "Victor", "test_run": "true"},
            content="This is a test of the new, simplified persistence layer."
        )
        
        logger.info(f"Test Object Created: ID = {test_event_id}")

        # 3. Test SQL storage
        logger.info("--- Testing SQL storage... ---")
        sql_store_object(test_object)
        retrieved_obj = sql_get_object_by_id(test_event_id)
        
        if retrieved_obj and retrieved_obj.event_id == test_event_id:
            logger.info("SUCCESS: SQL store/retrieve test passed.")
            assert retrieved_obj.content == "This is a test of the new, simplified persistence layer."
            assert retrieved_obj.source_details["user"] == "Victor"
        else:
            logger.error(f"FAILURE: SQL store/retrieve test failed. Retrieved: {retrieved_obj}")

        # 4. Test Vector storage
        logger.info("--- Testing Vector storage... ---")
        vector_add_embedding(test_object)
        # Small delay to ensure persistence
        import time
        time.sleep(1)
        search_results = vector_search("What is the simplified layer for?", top_k=1)
        
        if search_results and "simplified persistence layer" in search_results[0]:
             logger.info(f"SUCCESS: Vector add/search test passed. Found: '{search_results[0][:50]}...'")
        else:
             logger.error(f"FAILURE: Vector add/search test failed. Results: {search_results}")

        # 5. Test SQL Update
        logger.info("--- Testing SQL update... ---")
        summary_text = "The object was successfully tested."
        linked_ids = ["link-1", "link-2"]
        sql_update_evaluated_object(test_event_id, summary_text, linked_ids)
        updated_obj = sql_get_object_by_id(test_event_id)

        if updated_obj and updated_obj.evaluation_outcome_summary == summary_text:
            logger.info("SUCCESS: SQL update test passed.")
            assert updated_obj.linked_event_ids == linked_ids
        else:
            logger.error("FAILURE: SQL update test failed.")


    except Exception as e:
        logger.critical(f"A critical error occurred during the standalone test: {e}", exc_info=True)
    
    logger.info("--- [END] Standalone Test for daeira_memory.py ---")
