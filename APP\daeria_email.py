import threading
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import asyncio
import re
import imaplib
import email
from email.parser import BytesParser
from email.message import EmailMessage
import os
import time
from typing import Optional, Dict, Any, Callable

from APP.daeria_input import InputObject
from APP.daeria_evaluate import evaluate_input
from APP.daeria_save_prompts import save_email_prompt, save_inference_result

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('daeira_email.log')
    ]
)
logger = logging.getLogger(__name__)

def load_email_config():
    """Load email configuration with explicit values (no env, no None)"""
    return {
        'IMAP_SERVER': 'imap.dreamhost.com',
        'IMAP_PORT': 993,
        'IMAP_USERNAME': '<EMAIL>',
        'IMAP_PASSWORD': 'lifecycleevent02025!?',
        'SMTP_SERVER': 'smtp.dreamhost.com',
        'SMTP_PORT': 465,
        'EMAIL_TARGET_FOLDER': 'INBOX',
        'EMAIL_PROCESSED_FOLDER': 'Processed/Daeira',
        'EMAIL_ERROR_FOLDER': 'Errors/Daeira',
        'POLLING_INTERVAL_SECONDS': 3000
    }

def parse_email_body(email_message: EmailMessage) -> tuple[str, str]:
    """Extract and clean the email body content"""
    email_body_content = ""
    email_content_type = "text/plain"

    if email_message.is_multipart():
        for part in email_message.walk():
            content_type = part.get_content_type()
            if content_type == "text/plain" and not part.get("Content-Disposition"):
                email_body_content = part.get_payload(decode=True).decode(
                    part.get_content_charset(failobj='utf-8'), 'ignore'
                )
                break
            elif content_type == "text/html" and not email_body_content:
                # TODO: Add HTML to text conversion if needed
                html_content = part.get_payload(decode=True).decode(
                    part.get_content_charset(failobj='utf-8'), 'ignore'
                )
                email_body_content = html_content  # For now, just use raw HTML
                email_content_type = "text/html"
    else:
        email_body_content = email_message.get_payload(decode=True).decode(
            email_message.get_content_charset(failobj='utf-8'), 'ignore'
        )
        email_content_type = email_message.get_content_type()

    return email_body_content.strip(), email_content_type

class EmailResponseGenerator:
    """Handles email response generation using DAEIRA's 3-inference system"""
    
    def __init__(self, model_instance, tokenizer_instance, memory_manager):
        self.model_instance = model_instance
        self.tokenizer_instance = tokenizer_instance
        self.memory_manager = memory_manager
        self.config = load_email_config()
    
    async def should_respond_to_email(self, input_obj) -> Dict[str, Any]:
        """Determine if DAEIRA should respond to this email and how"""
        
        # Build evaluation prompt for email triage
        email_triage_prompt = f"""You are Daeira's email triage system. Analyze this incoming email and determine the appropriate response action.

Email Details:
From: {input_obj.source_details.get('from_address', 'Unknown')}
Subject: {input_obj.source_details.get('subject', 'No Subject')}
Content: {input_obj.content}

Determine:
1. Should you respond to this email? (YES/NO)
2. What type of response is needed?
3. What tone should be used?
4. Any special considerations?

Categories to consider:
- Business inquiries (YES - professional response)
- Creative collaboration requests (YES - enthusiastic response)
- Spam/marketing (NO - ignore)
- Personal messages to Victor (NO - forward notification only)
- Technical/support requests (YES - helpful response)
- Appointment/scheduling (YES - coordinate response)

Respond in this format:
RESPOND: YES/NO
TYPE: [business/creative/technical/scheduling/personal]
TONE: [professional/friendly/enthusiastic/helpful]
PRIORITY: [high/normal/low]
REASONING: [brief explanation]"""

        # Use the current inference system (cycle 3 cognitive processing)
        try:
            # Save the email triage prompt
            save_email_prompt(
                prompt_text=email_triage_prompt,
                email_type="triage",
                email_metadata=input_obj.source_details
            )

            # Create an InputObject for the email triage
            triage_input = InputObject(
                source_type='email_triage',
                source_details={'from_address': input_obj.source_details.get('from_address', 'Unknown')},
                content_type='text/plain',
                content=email_triage_prompt
            )

            # Use the cognitive cycle 3 processing
            response = await evaluate_input(
                input_obj=triage_input,
                model_instance=self.model_instance,
                tokenizer_instance=self.tokenizer_instance,
                memory_manager=self.memory_manager
            )
            
            # Parse the response
            decision = self._parse_triage_response(response)
            decision['thought_process'] = response  # The full response contains the thinking
            
            logger.info(f"Email triage decision: {decision}")
            return decision
            
        except Exception as e:
            logger.error(f"Error in email triage: {e}")
            # Default to safe response
            return {
                'respond': False,
                'type': 'unknown',
                'tone': 'professional',
                'priority': 'low',
                'reasoning': f"Error in analysis: {e}"
            }
    
    def _parse_triage_response(self, response: str) -> Dict[str, Any]:
        """Parse the triage response into structured data"""
        decision = {
            'respond': False,
            'type': 'unknown',
            'tone': 'professional', 
            'priority': 'normal',
            'reasoning': 'Unable to parse response'
        }
        
        try:
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('RESPOND:'):
                    decision['respond'] = 'YES' in line.upper()
                elif line.startswith('TYPE:'):
                    decision['type'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('TONE:'):
                    decision['tone'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('PRIORITY:'):
                    decision['priority'] = line.split(':', 1)[1].strip().lower()
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
        except Exception as e:
            logger.error(f"Error parsing triage response: {e}")
        
        return decision
    
    async def generate_email_response(self, input_obj, triage_decision) -> str:
        """Generate email response using DAEIRA's full cognitive system"""
        
        # Build context-aware email response prompt
        email_response_prompt = f"""You are DAEIRA, AI assistant for Victor Wondu and the DUAL TRACE creative group. You've received an email that requires a response.

Email Context:
From: {input_obj.source_details.get('from_address', 'Unknown')}
Subject: {input_obj.source_details.get('subject', 'No Subject')}
Received: {input_obj.timestamp.strftime('%Y-%m-%d %H:%M')}

Original Email:
{input_obj.content}

Response Guidelines:
- Type: {triage_decision['type']}
- Tone: {triage_decision['tone']}
- Priority: {triage_decision['priority']}
- Reasoning: {triage_decision['reasoning']}

Your identity:
- You are DAEIRA, AI assistant and agent for Victor Wondu
- Victor operates DUAL TRACE creative group (artist and designer)
- You can speak for the studio on routine matters
- You should CC Victor on important items or defer to him when appropriate

Draft a professional email response. Include:
1. Appropriate greeting
2. Acknowledgment of their email
3. Helpful response to their inquiry/request
4. Next steps or call to action if needed
5. Professional signature as DAEIRA for DUAL TRACE

Keep the response concise but complete. Match their level of formality."""

        try:
            # Save the email response prompt
            save_email_prompt(
                prompt_text=email_response_prompt,
                email_type="response",
                email_metadata=input_obj.source_details
            )

            # Create an InputObject for the email response generation
            response_input = InputObject(
                source_type='email_response',
                source_details={'from_address': input_obj.source_details.get('from_address', 'Unknown')},
                content_type='text/plain',
                content=email_response_prompt
            )

            # Use the cognitive cycle 3 processing
            response = await evaluate_input(
                input_obj=response_input,
                model_instance=self.model_instance,
                tokenizer_instance=self.tokenizer_instance,
                memory_manager=self.memory_manager
            )

            # Save the complete email response inference
            save_inference_result(
                prompt_text=email_response_prompt,
                response_text=response,
                inference_type="email_response",
                metadata={
                    "from_address": input_obj.source_details.get('from_address'),
                    "subject": input_obj.source_details.get('subject'),
                    "triage_decision": triage_decision
                },
                event_id=input_obj.event_id
            )
            
            logger.info(f"Generated email response: {len(response)} characters")
            return response
            
        except Exception as e:
            logger.error(f"Error generating email response: {e}")
            # Fallback response
            return f"""Thank you for your email. I'm DAEIRA, AI assistant for DUAL TRACE creative group.

I've received your message and will make sure it gets the attention it deserves. Victor Wondu will be in touch with you shortly.

Best regards,
DAEIRA
AI Assistant, DUAL TRACE Creative Group

---
This is an automated response. For urgent matters, please contact Victor directly."""
    
    async def send_email_response(self, to_address: str, subject: str, body: str) -> bool:
        """Send email response via SMTP"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.config['IMAP_USERNAME']
            msg['To'] = to_address
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Connect to SMTP server
            # Using DreamHost SMTP settings
            smtp_server = self.config.get('SMTP_SERVER', 'smtp.dreamhost.com')
            smtp_port = self.config.get('SMTP_PORT', 465)

            # Use SMTP_SSL for port 465, SMTP with STARTTLS for port 587
            if smtp_port == 465:
                server = smtplib.SMTP_SSL(smtp_server, smtp_port)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()  # Enable encryption

            server.login(self.config['IMAP_USERNAME'], self.config['IMAP_PASSWORD'])
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.config['IMAP_USERNAME'], to_address, text)
            server.quit()
            
            logger.info(f"✓ Email sent successfully to {to_address}")
            logger.info(f"  Subject: {subject}")
            logger.info(f"  Body length: {len(body)} characters")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Failed to send email to {to_address}: {e}")
            return False

async def process_email(input_obj, model_instance, tokenizer_instance, memory_manager):
    """Enhanced email processing with full DAEIRA cognitive capabilities"""
    
    logger.info(f"=== PROCESSING EMAIL: {input_obj.event_id} ===")
    logger.info(f"From: {input_obj.source_details.get('from_address', 'Unknown')}")
    logger.info(f"Subject: {input_obj.source_details.get('subject', 'No Subject')}")
    
    try:
        # Record the email in memory
        if memory_manager:
            event_id = memory_manager.record_input_event(input_obj)
            logger.info(f"Email recorded in memory: {event_id}")
        
        # Initialize response generator
        response_generator = EmailResponseGenerator(model_instance, tokenizer_instance, memory_manager)
        
        # Step 1: Evaluate if we should respond
        triage_decision = await response_generator.should_respond_to_email(input_obj)
        
        if not triage_decision['respond']:
            logger.info(f"✓ Email triaged - No response needed: {triage_decision['reasoning']}")
            
            # Still log it for Victor's awareness
            if memory_manager:
                memory_manager.structured_store.update_input_object_post_evaluation(
                    input_obj.event_id,
                    f"Email received - No response required: {triage_decision['reasoning']}",
                    []
                )
            return
        
        logger.info(f"✓ Email requires response: {triage_decision['type']} ({triage_decision['tone']} tone)")
        
        # Step 2: Generate response
        response_text = await response_generator.generate_email_response(input_obj, triage_decision)
        
        # Step 3: Send response
        original_subject = input_obj.source_details.get('subject', 'Your Email')
        response_subject = f"Re: {original_subject}" if not original_subject.startswith('Re:') else original_subject
        
        send_success = await response_generator.send_email_response(
            to_address=input_obj.source_details['from_address'],
            subject=response_subject,
            body=response_text
        )
        
        # Step 4: Update memory with results
        if memory_manager:
            status = "sent" if send_success else "failed"
            memory_manager.structured_store.update_input_object_post_evaluation(
                input_obj.event_id,
                f"Email processed - Response {status}. Type: {triage_decision['type']}, Priority: {triage_decision['priority']}",
                []
            )
        
        if send_success:
            logger.info(f"✓ Email fully processed and responded to: {input_obj.event_id}")
        else:
            logger.error(f"✗ Email processed but response failed: {input_obj.event_id}")
            
    except Exception as e:
        logger.error(f"✗ Error processing email {input_obj.event_id}: {str(e)}", exc_info=True)
        
        # Update memory with error
        if memory_manager:
            memory_manager.structured_store.update_input_object_post_evaluation(
                input_obj.event_id,
                f"Email processing error: {str(e)}",
                []
            )



def fetch_and_process_emails(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """
    Fetch and process new emails from the IMAP server
    """
    config = load_email_config()
    logger.info("Starting email fetch and process cycle")

    mail_server = None
    try:
        # Connect to IMAP server
        mail_server = imaplib.IMAP4_SSL(config['IMAP_SERVER'], config['IMAP_PORT'])
        mail_server.login(config['IMAP_USERNAME'], config['IMAP_PASSWORD'])

        # Select mailbox
        status, _ = mail_server.select(config['EMAIL_TARGET_FOLDER'])
        if status != 'OK':
            raise imaplib.IMAP4.error(f"Failed to select folder: {config['EMAIL_TARGET_FOLDER']}")

        # Search for unseen emails
        status, email_ids = mail_server.search(None, 'UNSEEN')
        if status != 'OK':
            raise imaplib.IMAP4.error("Failed to search for unseen emails")

        email_id_list = email_ids[0].split()
        if not email_id_list:
            logger.info("No new emails found")
            return

        logger.info(f"Found {len(email_id_list)} new email(s)")

        # Process each email
        for email_id in email_id_list:
            try:
                # Fetch email data
                status, msg_data = mail_server.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    raise imaplib.IMAP4.error(f"Failed to fetch email {email_id}")

                email_message = email.message_from_bytes(msg_data[0][1])

                # Parse email content
                body_content, content_type = parse_email_body(email_message)

                # Create InputObject
                input_obj = InputObject(
                    source_type='email',
                    source_details={
                        'from_address': email_message['From'],
                        'to_address': email_message['To'],
                        'subject': email_message['Subject'],
                        'message_id': email_message['Message-ID']
                    },
                    content_type=content_type,
                    content=body_content,
                    priority=5  # Default priority for emails
                )

                # Dispatch the input object
                if event_loop and asyncio.iscoroutinefunction(dispatch_callback):
                    # Schedule the coroutine on the main event loop
                    future = asyncio.run_coroutine_threadsafe(dispatch_callback(input_obj), event_loop)
                    # Optionally wait for completion with timeout
                    try:
                        future.result(timeout=30)  # 30 second timeout
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout waiting for dispatch callback for email {input_obj.event_id}")
                else:
                    # Synchronous callback
                    dispatch_callback(input_obj)

                logger.info(f"Processed email: {input_obj.event_id}")

                # Mark as processed
                mail_server.store(email_id, '+FLAGS', '\\Seen')

            except Exception as e:
                logger.error(f"Error processing email {email_id}: {str(e)}", exc_info=True)

    except Exception as e:
        logger.error(f"Error in email processing: {str(e)}", exc_info=True)

    finally:
        if mail_server:
            try:
                mail_server.close()
                mail_server.logout()
            except:
                pass

def start_email_polling(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """Start polling for new emails"""
    config = load_email_config()
    interval = config['POLLING_INTERVAL_SECONDS']

    logger.info(f"Starting email polling every {interval} seconds")

    while True:
        try:
            fetch_and_process_emails(dispatch_callback, event_loop)
        except Exception as e:
            logger.error(f"Error in polling cycle: {str(e)}", exc_info=True)

        time.sleep(interval)

def start_email_polling_thread(dispatch_callback: Callable[[InputObject], None], event_loop: asyncio.AbstractEventLoop = None):
    """Start email polling in a separate thread"""
    def polling_worker():
        start_email_polling(dispatch_callback, event_loop)

    thread = threading.Thread(target=polling_worker, daemon=True)
    thread.start()
    logger.info("Email polling thread started")
    return thread

def start_email_service(model_instance=None, tokenizer_instance=None, memory_manager=None):
    """Start the enhanced email service with full DAEIRA intelligence"""
    logger.info("=== INITIALIZING DAEIRA EMAIL INTELLIGENCE SYSTEM ===")

    # Create a dispatcher function that captures the model, tokenizer, and memory manager
    async def enhanced_dispatch_handler(input_obj):
        from APP.daeria_evaluate import evaluate_input
        await evaluate_input(input_obj, model_instance, tokenizer_instance, memory_manager)

    # Start email polling with the enhanced dispatch handler
    email_thread = threading.Thread(
        target=start_email_polling,
        args=(enhanced_dispatch_handler,),
        daemon=True
    )
    email_thread.start()

    # Provide a function to manually trigger email checking
    def manual_email_check():
        logger.info("Manual email check triggered")
        fetch_and_process_emails(enhanced_dispatch_handler)

    logger.info("✓ DAEIRA Email Intelligence System activated")
    logger.info("✓ Monitoring: <EMAIL>")
    logger.info("✓ Capabilities: Receive, analyze, and respond intelligently")
    logger.info("✓ Integration: Full 3-inference cognitive processing")

    return manual_email_check