from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig  # Add AutoModelForCausalLM import
from app.inference import DAEIRAInferenceSystem  # Import DAEIRAInferenceSystem
from app.daeria_memory import MemoryManagerDaeira, StructuredMemoryStore, VectorMemoryStore, initialize_database, get_db_config
from app.daeria_input import InputObject, start_email_polling_thread  # Import start_email_polling_thread
from app.daeria_email import process_input  # Import process_input
from app.daeria_documents import EnhancedDocumentProcessor  # Import EnhancedDocumentProcessor
import traceback
from typing import List, Optional, Tuple  # Add missing import for Tuple
import pyttsx3  # Add missing import for pyttsx3
import threading  # Add missing import for threading
import asyncio  # Add missing import for asyncio
from datetime import datetime  # Added to fix NameError for datetime
import json  # Added to fix NameError for json
import uuid  # Added to fix NameError for uuid
import re  # Added to fix NameError for re
import mysql.connector  # Added to fix NameError for mysql.connector
import tempfile  # Added to fix NameError for tempfile

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('daeira_main.log')
    ]
)
logger = logging.getLogger(__name__)

# Hard-coded paths (no .env variables)
MODEL_PATH = r"C:\daeira\models\R1_distill_7B"  # Ensure this path is correct
PROMPT_SAVE_DIR = r"C:\DAEIRA\PROMPTS"
INFERENCE_SAVE_DIR = r"C:\DAEIRA\INFERENCE"
CONVERSATION_JSON_FILE = r"C:\DAEIRA\conversation.json"
STUDY_DIRECTORY = r"C:\DAEIRA\STUDY"
CHROMADB_PATH = r"C:\DAEIRA\MEMORY"
MAX_TOKENS = 130000

# Global instances
tokenizer_instance = None
memory_manager = None
document_processor = None
tts_instance = None
conversation_manager = None
model_instance = None
daeira_system = None

class ConversationManager:
    def __init__(self):
        self.json_file = CONVERSATION_JSON_FILE
        self.conversation_history = []
        self.load_from_json()
    
    def load_from_json(self):
        """Load active conversation from JSON"""
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversation_history = data.get("messages", [])
                    logger.info(f"Loaded {len(self.conversation_history)} messages from JSON")
            else:
                logger.info("No existing conversation JSON found, starting fresh")
                self.conversation_history = []
        except Exception as e:
            logger.error(f"Failed to load conversation from JSON: {e}")
            self.conversation_history = []
    
    def save_to_json(self):
        """Save active conversation to JSON"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.json_file), exist_ok=True)
            
            conversation_data = {
                "session_id": str(uuid.uuid4()),
                "last_updated": datetime.utcnow().isoformat(),
                "message_count": len(self.conversation_history),
                "messages": self.conversation_history
            }
            
            # Atomic write using temp file
            temp_file = f"{self.json_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, indent=2, ensure_ascii=False)
            
            # Atomic move
            os.replace(temp_file, self.json_file)
            logger.info(f"Saved {len(self.conversation_history)} messages to JSON")
            
        except Exception as e:
            logger.error(f"Failed to save conversation to JSON: {e}")
    
    def add_message(self, role: str, content: str):
        """Add message and save immediately"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": str(uuid.uuid4())
        }
        self.conversation_history.append(message)
        self.save_to_json()
        
        # Also save to SQL for permanent archive
        asyncio.create_task(self.archive_to_sql(role, content))
    
    async def archive_to_sql(self, role: str, content: str):
        """Background archive to SQL"""
        try:
            if role == "user":
                # Will be paired with next assistant message
                self._pending_user_message = content
            elif role == "assistant" and hasattr(self, '_pending_user_message'):
                save_to_database(self._pending_user_message, content)
                delattr(self, '_pending_user_message')
        except Exception as e:
            logger.error(f"Failed to archive to SQL: {e}")
    
    def get_conversation_for_prompt(self) -> str:
        """Fast conversation retrieval for prompt construction"""
        history_parts = []
        for msg in self.conversation_history:
            role = msg["role"]
            content = msg["content"]
            
            if role.lower() in ["user", "victor"]:
                history_parts.append(f"Victor: {content}")
            elif role.lower() in ["assistant", "dahlia"]:
                history_parts.append(f"Dahlia: {content}")
            elif role.lower() == "system" and content:
                # Skip system messages in history display
                continue
        
        return "\n".join(history_parts)
    
    def trim_if_needed(self, max_tokens: int = 130000):
        """Trim conversation if too long"""
        # Use app.state.tokenizer_instance if available, else fallback to global
        try:
            from fastapi import Request
            import starlette
            # Try to get the running app instance
            import sys
            app = sys.modules.get("app.main").app
            tokenizer = getattr(app.state, "tokenizer_instance", None)
        except Exception:
            tokenizer = None
        if not tokenizer:
            global tokenizer_instance
            tokenizer = tokenizer_instance
        if not tokenizer:
            return

        current_tokens = sum(len(tokenizer.encode(msg["content"])) 
                           for msg in self.conversation_history)
        
        if current_tokens > max_tokens:
            # Keep system prompt + last 10 exchanges
            system_messages = [msg for msg in self.conversation_history if msg["role"] == "system"]
            recent_messages = self.conversation_history[-20:]  # Last 10 exchanges
            
            self.conversation_history = system_messages + recent_messages
            self.save_to_json()
            
            logger.warning(f"Trimmed conversation: {current_tokens} -> estimated {current_tokens * 0.3}")
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        self.save_to_json()
        logger.info("Conversation history cleared")





class DahliaTTS:
    def __init__(self):
        self.engine = pyttsx3.init()
        self.is_speaking = False
        self.speaking_lock = threading.Lock()
        self._configure_voice()
    
    def _configure_voice(self):
        voices = self.engine.getProperty('voices')
        # Explicitly set voice to "Sonia" (ensure this voice is installed locally)
        for voice in voices:
            if "sonia" in voice.name.lower():
                self.engine.setProperty("voice", voice.id)
                break
        else:
            # Fallback: select any female voice
            for voice in voices:
                if "female" in voice.name.lower():
                    self.engine.setProperty("voice", voice.id)
                    break
        
        self.engine.setProperty('rate', 180)
        self.engine.setProperty('volume', 0.8)
    
    def speak_sync(self, text: str):
        with self.speaking_lock:
            if self.is_speaking:
                return
            
            self.is_speaking = True
            
            try:
                self.engine.say(text)
                self.engine.runAndWait()
            finally:
                self.is_speaking = False
    
    def speak_async(self, text: str):
        if self.is_speaking:
            return
            
        thread = threading.Thread(target=self.speak_sync, args=(text,))
        thread.daemon = True
        thread.start()
    
    def stop(self):
        with self.speaking_lock:
            if self.is_speaking:
                self.engine.stop()
                self.is_speaking = False
    
    def is_available(self) -> bool:
        return self.engine is not None

class MessageRole:
    SYSTEM = "system"
    VICTOR = "Victor"
    DAHLIA = "Dahlia"
    USER = "Victor" 
    ASSISTANT = "Dahlia"

app = FastAPI(title="DAHLIA", description="DAHLIA OF DUAL TRACE")

@app.on_event("startup")
async def startup():
    """Initialize system on startup"""
    global dahlia_system, model_instance, tokenizer_instance
    global memory_manager, document_processor, tts_instance, conversation_manager

    try:
        logger.info("Starting DAEIRA system initialization...")

        # Load model and tokenizer
        model_instance = load_model(MODEL_PATH)
        tokenizer_instance = AutoTokenizer.from_pretrained(MODEL_PATH)
        # Save tokenizer_instance to app.state for later use in trim_if_needed
        app.state.tokenizer_instance = tokenizer_instance

        # Initialize DAEIRAInferenceSystem
        daeira_system = DAEIRAInferenceSystem(model_instance, tokenizer_instance)
        app.state.daeira_system = daeira_system
        logger.info("✓ DAEIRAInferenceSystem initialized successfully")
        
        # Initialize ConversationManager
        conversation_manager = ConversationManager()
        app.state.conversation_manager = conversation_manager
        logger.info("✓ ConversationManager initialized successfully")

        # Initialize MemoryManager
        memory_manager = initialize_memory_manager()
        app.state.memory_manager = memory_manager
        logger.info("✓ MemoryManager initialized successfully")

        # Initialize DocumentProcessor if EnhancedDocumentProcessor is available
        if memory_manager:
            document_processor = EnhancedDocumentProcessor(
                memory_manager=memory_manager,
                study_directory=STUDY_DIRECTORY
            )
            app.state.document_processor = document_processor
            logger.info("✓ DocumentProcessor initialized successfully")
        else:
            document_processor = None
            app.state.document_processor = None
            logger.warning("DocumentProcessor not initialized due to missing memory_manager")

        # Initialize TTS
        tts_instance = initialize_tts()
        app.state.tts_instance = tts_instance
        logger.info("✓ TTS system initialized successfully")

    except Exception as e:
        logger.error(f"✗ Failed to initialize DAHLIA system: {e}", exc_info=True)
        # Do not raise RuntimeError here, just log the error and let FastAPI continue startup

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    prompt: str

class ClearHistoryRequest(BaseModel):
    pass

class DocumentStudyRequest(BaseModel):
    document_path: str
    document_title: Optional[str] = None
    tags: Optional[List[str]] = None

class EmailCheckRequest(BaseModel):
    pass

def load_model(model_path: str):
    """Load the model from the specified path"""
    if torch.cuda.is_available():
        device = torch.device("cuda")
        logger.info("CUDA is available. Using GPU for inference.")
    else:
        device = torch.device("cpu")
        logger.info("CUDA not available. Using CPU for inference.")
    torch.cuda.empty_cache()  # Clear unused GPU memory to reduce fragmentation
    quantization_config = BitsAndBytesConfig(
        load_in_8bit=True,
        llm_int8_enable_fp32_cpu_offload=True  # Enable CPU offload for 32-bit modules
    )
    model_instance = AutoModelForCausalLM.from_pretrained(
        model_path,  # Use the passed model_path
        quantization_config=quantization_config,
        device_map="auto" if torch.cuda.is_available() else None,
    )
    logger.info(f"Model loaded successfully on device: {model_instance.device}")
    return model_instance

def save_prompt (prompt_text: str, filename_prefix: str):
    """Save prompt text to file in PROMPT_SAVE_DIR"""
    try:
        if not os.path.exists(PROMPT_SAVE_DIR):
            os.makedirs(PROMPT_SAVE_DIR)
            logger.info(f"Created prompt save directory: {PROMPT_SAVE_DIR}")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"{filename_prefix}_{timestamp}.txt"
        filepath = os.path.join(PROMPT_SAVE_DIR, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(prompt_text)
        logger.info(f"Prompt saved to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Failed to save prompt to file: {e}", exc_info=True)
        return None

def _save_inference_result(level_name: str, prompt: str, response: str, event_id: str):
    """Save inference prompt and result to INFERENCE_SAVE_DIR"""
    try:
        if not os.path.exists(INFERENCE_SAVE_DIR):
            os.makedirs(INFERENCE_SAVE_DIR)
            logger.info(f"Created inference save directory: {INFERENCE_SAVE_DIR}")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"{level_name}_{event_id}_{timestamp}.txt"
        filepath = os.path.join(INFERENCE_SAVE_DIR, filename)
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(f"=== {level_name.upper()} INFERENCE ===\n")
            f.write(f"Event ID: {event_id}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Level: {level_name}\n\n")
            f.write("=== PROMPT ===\n")
            f.write(prompt)
            f.write("\n\n=== RESPONSE ===\n")
            f.write(response)
            f.write("\n\n")
        
        logger.info(f"Inference result saved to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Failed to save inference result: {e}", exc_info=True)
        return None

def extract_thought_and_response(full_text: str) -> Tuple[str, str]:
    """Extract thought process and main response from model output"""
    thought_process = ""
    main_response = full_text

    closed_match = re.search(r'<think>(.*?)</think>', full_text, flags=re.DOTALL)
    
    if closed_match:
        thought_process = closed_match.group(1).strip()
        main_response = re.sub(r'<think>.*?</think>', '', full_text, flags=re.DOTALL).strip()
        logger.info(f"Extracted thought process: {len(thought_process)} chars")
        logger.info(f"Main response after extraction: {len(main_response)} chars")
    else:
        open_match = re.search(r'<think>(.*)', full_text, flags=re.DOTALL)
        if open_match:
            thought_process = open_match.group(1).strip()
            main_response = ""
            logger.warning("Found unclosed <think> tag - treating all content as thought process")
        else:
            logger.info("No <think> tags found in model output")

    return thought_process, main_response

def initialize_memory_manager():
    """Initialize the memory system from daeria_memory.py"""
    try:
        memory_config = {
            "mysql": {
                "host": "localhost",
                "user": "daeira",
                "password": "Daeiresponse25654",
                "database": "daeira"
            },
            "chromadb": {
                "persistence_path": CHROMADB_PATH,
                "collection_name": "daeira_knowledge_base",
                "embedding_model": "all-MiniLM-L6-v2"
            }
        }
        logger.info(f"MemoryManagerDaeira config: {memory_config}")
        logger.info("Initializing MemoryManagerDaeira...")
        memory_manager = MemoryManagerDaeira(memory_config)
        logger.info("MemoryManagerDaeira initialized successfully")
        return memory_manager
    except Exception as e:
        logger.error(f"Failed to initialize memory manager: {e}", exc_info=True)
        logger.error(traceback.format_exc())
        return None

""" def initialize_document_processor(memory_manager):
    try:
        document_processor = DocumentProcessor(
            memory_manager=memory_manager,
            study_directory=STUDY_DIRECTORY
        )
        logger.info(f"Document processor initialized with study directory: {STUDY_DIRECTORY}")
        return document_processor
    except Exception as e:
        logger.error(f"Failed to initialize document processor: {e}", exc_info=True)
        return None """

def initialize_tts() -> DahliaTTS:
    global tts_instance
    if tts_instance is None:
        tts_instance = DahliaTTS()
        logger.info("TTS system initialized")
    return tts_instance

def save_interaction_to_memory(user_message: str, assistant_message: str):
    """Save conversation interaction to memory system"""
    global memory_manager
    
    try:
        if memory_manager:
            victor_input = InputObject(
                event_id=str(uuid.uuid4()),
                timestamp=datetime.utcnow(),
                source_type="user_message",
                source_details={"from": "victor", "interface": "chat"},
                content_type="text/plain",
                content=user_message,
                initial_tags=["conversation", "victor_input", "chat_interface"]
            )
            
            assistant_input = InputObject(
                event_id=str(uuid.uuid4()),
                timestamp=datetime.utcnow(),
                source_type="dahlia_response",
                source_details={"from": "dahlia", "interface": "chat"},
                content_type="text/plain",
                content=assistant_message,
                initial_tags=["conversation", "dahlia_output", "chat_interface"]
            )
            
            victor_event_id = memory_manager.record_input_event(victor_input)
            assistant_event_id = memory_manager.record_input_event(assistant_input)
            
            memory_manager.structured_store.update_input_object_post_evaluation(
                victor_event_id,
                "Victor message processed with assistant response",
                [assistant_event_id]
            )
            
            logger.info(f"Conversation turn saved to memory system. Victor: {victor_event_id}, Assistant: {assistant_event_id}")
    except Exception as e:
        logger.error(f"Failed to save interaction to memory: {e}", exc_info=True)

def save_to_database(user_message: str, assistant_message: str):
    """Save conversation to MySQL database"""
    try:
        connection = mysql.connector.connect(
            host="localhost",
            user="dahlia",
            password="Dahliresponse25654",
            database="dahlia"
        )
        cursor = connection.cursor()
        # Ensure the conversations table exists before inserting
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conversations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_message TEXT,
                assistant_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        query = "INSERT INTO conversations (user_message, assistant_message) VALUES (%s, %s)"
        cursor.execute(query, (user_message, assistant_message))
        connection.commit()
        logger.info("Conversation turn saved to database.")
    except mysql.connector.Error as err:
        logger.error(f"Database error: {err}")
    except Exception as e:
        logger.error(f"Unexpected error in save_to_database: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            if 'cursor' in locals() and cursor:
                 cursor.close()
            connection.close()


@app.post("/chat")
async def chat(request: ChatRequest):
    """Main chat endpoint"""
    dahlia_system = app.state.dahlia_system
    conversation_manager = app.state.conversation_manager
    tts_instance = getattr(app.state, "tts_instance", None)

    if not dahlia_system:
        raise HTTPException(status_code=503, detail="DAHLIA system not initialized")

    try:
        prompt = request.prompt
        conversation_manager.add_message("user", prompt)
        conversation_manager.trim_if_needed(MAX_TOKENS)
        prompt_for_model = conversation_manager.get_conversation_for_prompt()
        save_prompt(prompt_for_model, "chat_prompt")
        # Use the correct method for inference (process or generate_response)
        if hasattr(dahlia_system, "generate_response"):
            response = await dahlia_system.generate_response(prompt_for_model)
        else:
            response = await dahlia_system.process(user_input=prompt_for_model)
        thought, main_response = extract_thought_and_response(response)
        # Strip hashmarks from the final response
        main_response = strip_hashmarks(main_response)
        conversation_manager.add_message("assistant", main_response)
        save_interaction_to_memory(prompt, main_response)
        if tts_instance and main_response:
            tts_instance.speak_async(main_response)
        if not main_response or not main_response.strip():
            main_response = "I'm sorry, I don't have a response at this time."
        logger.info(f"Returning response to UI: {main_response[:200]}")
        return {
            "response": main_response,
            "thought_process": thought
        }
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}", exc_info=True)
        return {
            "response": "An error occurred. Please try again later.",
            "thought_process": ""
        }

@app.post("/clear_history")
async def clear_history_endpoint(_: ClearHistoryRequest):
    """Clear the conversation history"""
    conversation_manager = app.state.conversation_manager
    conversation_manager.clear_history()
    logger.info("Conversation history cleared.")
    return JSONResponse({"message": "Conversation history cleared."})

@app.post("/study_document")
async def study_document_endpoint(request: DocumentStudyRequest):
    """Study a document and add it to memory"""
    document_processor = getattr(app.state, "document_processor", None)
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not initialized")
    try:
        doc_id = await document_processor.process_document(
            document_path=request.document_path,
            document_title=request.document_title,
            tags=request.tags or ["manual_study"]
        )
        return JSONResponse({
            "status": "success",
            "message": f"Document processed and added to memory successfully",
            "document_id": doc_id
        })
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.post("/upload_study_document")
async def upload_study_document(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    tags: Optional[str] = Form(None)
):
    """Upload and study a document"""
    document_processor = getattr(app.state, "document_processor", None)
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not initialized")
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_path = temp_file.name
        parsed_tags = []
        if tags:
            parsed_tags = [tag.strip() for tag in tags.split(",") if tag.strip()]
        parsed_tags.extend(["upload", "study"])
        doc_id = await document_processor.process_document(
            document_path=temp_path,
            document_title=title or file.filename,
            tags=parsed_tags
        )
        os.unlink(temp_path)
        return JSONResponse({
            "status": "success",
            "message": f"Document '{file.filename}' uploaded and processed successfully",
            "document_id": doc_id
        })
    except Exception as e:
        logger.error(f"Error uploading/processing document: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing uploaded document: {str(e)}")

@app.post("/scan_study_directory")
async def scan_study_directory_endpoint():
    """Manually trigger scan of study directory for new documents"""
    document_processor = getattr(app.state, "document_processor", None)
    if not document_processor:
        raise HTTPException(status_code=503, detail="Document processor not initialized")
    try:
        processed_ids = await document_processor.scan_study_directory()
        return JSONResponse({
            "status": "success",
            "message": f"Study directory scanned successfully",
            "processed_count": len(processed_ids),
            "processed_document_ids": processed_ids
        })
    except Exception as e:
        logger.error(f"Error scanning study directory: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error scanning study directory: {str(e)}")

@app.get("/system_status")
async def system_status():
    """Get the current status of all DAHLIA components"""
    model_instance = getattr(app.state, "dahlia_system", None)
    tokenizer_instance = getattr(app.state, "tokenizer_instance", None)
    memory_manager = getattr(app.state, "memory_manager", None)
    document_processor = getattr(app.state, "document_processor", None)
    tts_instance = getattr(app.state, "tts_instance", None)
    conversation_manager = getattr(app.state, "conversation_manager", None)
    status = {
        "model_loaded": model_instance is not None,
        "tokenizer_loaded": tokenizer_instance is not None,
        "memory_manager_initialized": memory_manager is not None,
        "document_processor_initialized": document_processor is not None,
        "tts_initialized": tts_instance is not None,
        "conversation_manager_initialized": conversation_manager is not None,
        "study_directory": STUDY_DIRECTORY,
        "prompt_save_directory": PROMPT_SAVE_DIR,
        "inference_save_directory": INFERENCE_SAVE_DIR,
        "conversation_json_file": CONVERSATION_JSON_FILE,
        "cuda_available": torch.cuda.is_available(),
        "timestamp": datetime.utcnow().isoformat()
    }
    if model_instance and hasattr(model_instance, 'model'):
        status["model_device"] = str(getattr(model_instance.model, 'device', 'unknown'))
    if memory_manager:
        try:
            recent_count = len(memory_manager.structured_store.get_recent_input_objects(limit=100))
            status["recent_memory_items"] = recent_count
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            status["memory_error"] = str(e)
    if conversation_manager:
        status["conversation_message_count"] = len(conversation_manager.conversation_history)
    return JSONResponse(status)

@app.get("/")
async def root():
    """Root endpoint with basic information"""
    return JSONResponse({"message": "Welcome to DAEIRA, the AI assistant for DUAL TRACE creative group."})

from contextlib import asynccontextmanager

# Remove unused/duplicate lifespan context manager to avoid confusion and errors.