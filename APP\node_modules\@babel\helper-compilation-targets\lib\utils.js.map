{"version": 3, "names": ["_semver", "require", "_helperValidatorOption", "_targets", "versionRegExp", "v", "OptionValidator", "semverMin", "first", "second", "semver", "lt", "semverify", "version", "valid", "invariant", "test", "toString", "pos", "num", "indexOf", "repeat", "isUnreleasedVersion", "env", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "getLowestUnreleased", "a", "b", "getHighestUnreleased", "getLowestImplementedVersion", "plugin", "environment", "result", "chrome"], "sources": ["../src/utils.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\nimport { unreleasedLabels } from \"./targets.ts\";\nimport type { Target, Targets } from \"./types.ts\";\n\nconst versionRegExp =\n  /^(?:\\d+|\\d(?:\\d?[^\\d\\n\\r\\u2028\\u2029]\\d+|\\d{2,}(?:[^\\d\\n\\r\\u2028\\u2029]\\d+)?))$/;\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nexport function semverMin(\n  first: string | undefined | null,\n  second: string,\n): string {\n  return first && semver.lt(first, second) ? first : second;\n}\n\n// Convert version to a semver value.\n// 2.5 -> 2.5.0; 1 -> 1.0.0;\nexport function semverify(version: number | string): string {\n  if (typeof version === \"string\" && semver.valid(version)) {\n    return version;\n  }\n\n  v.invariant(\n    typeof version === \"number\" ||\n      (typeof version === \"string\" && versionRegExp.test(version)),\n    `'${version}' is not a valid version`,\n  );\n\n  version = version.toString();\n\n  let pos = 0;\n  let num = 0;\n  while ((pos = version.indexOf(\".\", pos + 1)) > 0) {\n    num++;\n  }\n  return version + \".0\".repeat(2 - num);\n}\n\nexport function isUnreleasedVersion(\n  version: string | number,\n  env: Target,\n): boolean {\n  const unreleasedLabel =\n    // @ts-expect-error unreleasedLabel will be guarded later\n    unreleasedLabels[env];\n  return (\n    !!unreleasedLabel && unreleasedLabel === version.toString().toLowerCase()\n  );\n}\n\nexport function getLowestUnreleased(a: string, b: string, env: Target): string {\n  const unreleasedLabel:\n    | (typeof unreleasedLabels)[keyof typeof unreleasedLabels]\n    | undefined =\n    // @ts-expect-error unreleasedLabel is undefined when env is not safari\n    unreleasedLabels[env];\n  if (a === unreleasedLabel) {\n    return b;\n  }\n  if (b === unreleasedLabel) {\n    return a;\n  }\n  return semverMin(a, b);\n}\n\nexport function getHighestUnreleased(\n  a: string,\n  b: string,\n  env: Target,\n): string {\n  return getLowestUnreleased(a, b, env) === a ? b : a;\n}\n\nexport function getLowestImplementedVersion(\n  plugin: Targets,\n  environment: Target,\n): string {\n  const result = plugin[environment];\n  // When Android support data is absent, use Chrome data as fallback\n  if (!result && environment === \"android\") {\n    return plugin.chrome;\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAGA,MAAMG,aAAa,GACjB,iFAAiF;AAEnF,MAAMC,CAAC,GAAG,IAAIC,sCAAe,oCAAkB,CAAC;AAEzC,SAASC,SAASA,CACvBC,KAAgC,EAChCC,MAAc,EACN;EACR,OAAOD,KAAK,IAAIE,OAAM,CAACC,EAAE,CAACH,KAAK,EAAEC,MAAM,CAAC,GAAGD,KAAK,GAAGC,MAAM;AAC3D;AAIO,SAASG,SAASA,CAACC,OAAwB,EAAU;EAC1D,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIH,OAAM,CAACI,KAAK,CAACD,OAAO,CAAC,EAAE;IACxD,OAAOA,OAAO;EAChB;EAEAR,CAAC,CAACU,SAAS,CACT,OAAOF,OAAO,KAAK,QAAQ,IACxB,OAAOA,OAAO,KAAK,QAAQ,IAAIT,aAAa,CAACY,IAAI,CAACH,OAAO,CAAE,EAC9D,IAAIA,OAAO,0BACb,CAAC;EAEDA,OAAO,GAAGA,OAAO,CAACI,QAAQ,CAAC,CAAC;EAE5B,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAG,CAAC;EACX,OAAO,CAACD,GAAG,GAAGL,OAAO,CAACO,OAAO,CAAC,GAAG,EAAEF,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE;IAChDC,GAAG,EAAE;EACP;EACA,OAAON,OAAO,GAAG,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAGF,GAAG,CAAC;AACvC;AAEO,SAASG,mBAAmBA,CACjCT,OAAwB,EACxBU,GAAW,EACF;EACT,MAAMC,eAAe,GAEnBC,yBAAgB,CAACF,GAAG,CAAC;EACvB,OACE,CAAC,CAACC,eAAe,IAAIA,eAAe,KAAKX,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;AAE7E;AAEO,SAASC,mBAAmBA,CAACC,CAAS,EAAEC,CAAS,EAAEN,GAAW,EAAU;EAC7E,MAAMC,eAEO,GAEXC,yBAAgB,CAACF,GAAG,CAAC;EACvB,IAAIK,CAAC,KAAKJ,eAAe,EAAE;IACzB,OAAOK,CAAC;EACV;EACA,IAAIA,CAAC,KAAKL,eAAe,EAAE;IACzB,OAAOI,CAAC;EACV;EACA,OAAOrB,SAAS,CAACqB,CAAC,EAAEC,CAAC,CAAC;AACxB;AAEO,SAASC,oBAAoBA,CAClCF,CAAS,EACTC,CAAS,EACTN,GAAW,EACH;EACR,OAAOI,mBAAmB,CAACC,CAAC,EAAEC,CAAC,EAAEN,GAAG,CAAC,KAAKK,CAAC,GAAGC,CAAC,GAAGD,CAAC;AACrD;AAEO,SAASG,2BAA2BA,CACzCC,MAAe,EACfC,WAAmB,EACX;EACR,MAAMC,MAAM,GAAGF,MAAM,CAACC,WAAW,CAAC;EAElC,IAAI,CAACC,MAAM,IAAID,WAAW,KAAK,SAAS,EAAE;IACxC,OAAOD,MAAM,CAACG,MAAM;EACtB;EACA,OAAOD,MAAM;AACf", "ignoreList": []}