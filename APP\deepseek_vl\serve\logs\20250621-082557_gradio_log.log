2025-06-21 08:25:57,597 - gradio_logger - INFO - Loading DAEIRA model from: C:/DAEIRA/deepseek_R1_7B
2025-06-21 08:26:55,854 - gradio_logger - INFO - DAEIRA model loaded successfully. Device: cpu
2025-06-21 08:26:56,289 - gradio_logger - ERROR - Failed to initialize SQL database: name 'logger' is not defined
2025-06-21 08:26:56,292 - gradio_logger - ERROR - Failed to initialize vector database: name 'logger' is not defined
2025-06-21 08:26:56,302 - gradio_logger - INFO - Enhanced document processor initialized successfully
2025-06-21 08:26:58,354 - gradio_logger - INFO - TTS service initialized successfully
2025-06-21 08:26:58,358 - gradio_logger - INFO - Email service initialized successfully
2025-06-21 08:27:44,088 - gradio_logger - ERROR - Chat error: evaluate_input() missing 1 required positional argument: 'memory_manager'
Traceback (most recent call last):
  File "C:\DAEIRA\APP\daeira.py", line 220, in chat
    response_text = await evaluate_input(
                          ~~~~~~~~~~~~~~^
        input_obj=chat_input,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    
    
    )
    ^
TypeError: evaluate_input() missing 1 required positional argument: 'memory_manager'
