from typing import Optional, Dict, List, Union
from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid
import logging
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class InputObject(BaseModel):
    """Standardized input object for all incoming data to Daeira"""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    source_type: str
    source_details: Dict[str, Union[str, int, None]]
    content_type: str
    content: Union[str, Dict]
    priority: Optional[int] = None
    initial_tags: Optional[List[str]] = None

# process_input function removed - functionality consolidated into evaluate_input