"""
DAEIRA 3-Inference Cognitive Cycle
Simple elegance: Complete conversation history → 3 progressive cognitive passes → Response
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from APP.daeria_input import InputObject
from APP.daeria_save_prompts import save_prompt, save_inference_result

logger = logging.getLogger(__name__)

async def evaluate_input(
    input_obj: InputObject,
    model_instance,
    tokenizer_instance,
    memory_manager,
    conversation_history: Optional[List[Dict[str, str]]] = None
) -> str:
    """
    DAEIRA Universal Input Processing:
    - Email inputs: Route to specialized email processing
    - Other inputs: Run 3-inference cognitive cycle

    preva_Daeira_inference: "What should I do?" - Basic recognition and response formulation
    eva_Daeira_inference: "What should I do? How should I do it?" - Strategic thinking added
    Daeira_inference: Full deliberation - Deep, nuanced understanding with extensive processing

    Returns: Final response after processing
    """

    try:
        # Handle email inputs with specialized processing
        if input_obj.source_type == 'email':
            from APP.daeria_email import process_email
            await process_email(input_obj, model_instance, tokenizer_instance, memory_manager)
            return "Email processed successfully"  # Email processing doesn't return response text

        # Handle all other inputs with 3-inference cognitive cycle
        # Build complete conversation history (first user input is the "system prompt")
        full_history = build_complete_history(conversation_history or [], input_obj)

        # Save the initial prompt (plain text only)
        save_prompt(
            prompt_text=full_history,
            prompt_type=f"{input_obj.source_type}_initial"
        )

        # preva_Daeira_inference (Level 1)
        cycle_1_response = await preva_daeira_inference(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        # eva_Daeira_inference (Level 2)
        cycle_2_response = await eva_daeira_inference(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        # Daeira_inference (Level 3)
        final_response = await daeira_inference(
            full_history,
            model_instance,
            tokenizer_instance,
            input_obj
        )

        logger.info(f"DAEIRA 3-cycle cognitive processing complete:")
        logger.info(f"  - preva_Daeira_inference: {len(cycle_1_response)} chars")
        logger.info(f"  - eva_Daeira_inference: {len(cycle_2_response)} chars")
        logger.info(f"  - Daeira_inference: {len(final_response)} chars")
        logger.info(f"All inference levels activated with prompt saving enabled")
        return final_response
        
    except Exception as e:
        logger.error(f"Input processing failed: {e}")

        # Handle email fallback
        if input_obj.source_type == 'email':
            logger.error(f"Email processing failed for {input_obj.event_id}")
            return "Email processing failed"

        # Simple fallback for other inputs - direct response without cycles
        return await direct_response_fallback(input_obj, conversation_history or [], model_instance, tokenizer_instance)

async def preva_daeira_inference(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    preva_Daeira_inference: Basic Recognition
    "What should I do?"
    """

    cycle_1_prompt = f"""{full_history}

Daeira: <think>
What should I do?
</think>

"""

    try:
        # Save the preva_Daeira_inference prompt (plain text only)
        save_prompt(
            prompt_text=cycle_1_prompt,
            prompt_type=f"{input_obj.source_type}_preva_Daeira_inference"
        )

        response = await generate_response(
            cycle_1_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=800
        )

        # Save the complete preva_Daeira_inference result (plain text only)
        save_inference_result(
            prompt_text=cycle_1_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_preva_Daeira_inference"
        )

        logger.debug(f"preva_Daeira_inference complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"preva_Daeira_inference failed: {e}")
        return "Basic recognition processing failed."

async def eva_daeira_inference(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    eva_Daeira_inference: Strategic Planning
    "What should I do? How should I do it?"
    """

    cycle_2_prompt = f"""{full_history}

Daeira: <think>
What should I do?

How should I do it?
</think>

"""

    try:
        # Save the eva_Daeira_inference prompt (plain text only)
        save_prompt(
            prompt_text=cycle_2_prompt,
            prompt_type=f"{input_obj.source_type}_eva_Daeira_inference"
        )

        response = await generate_response(
            cycle_2_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=1200
        )

        # Save the complete eva_Daeira_inference result (plain text only)
        save_inference_result(
            prompt_text=cycle_2_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_eva_Daeira_inference"
        )

        logger.debug(f"eva_Daeira_inference complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"eva_Daeira_inference failed: {e}")
        return "Strategic planning processing failed."

async def daeira_inference(full_history: str, model_instance, tokenizer_instance, input_obj: InputObject) -> str:
    """
    Daeira_inference: Deep Deliberation
    "What should I do? How should I do it?" + extensive processing
    """

    cycle_3_prompt = f"""{full_history}

Daeira: <think>
What should I do?

How should I do it?

[Deep cognitive processing - considering full conversational context, relationships, nuances, implications, and optimal response approach]
</think>

"""
    
    try:
        # Save the Daeira_inference prompt (plain text only)
        save_prompt(
            prompt_text=cycle_3_prompt,
            prompt_type=f"{input_obj.source_type}_Daeira_inference"
        )

        response = await generate_response(
            cycle_3_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=2000
        )

        # Save the complete Daeira_inference result (plain text only)
        save_inference_result(
            prompt_text=cycle_3_prompt,
            response_text=response,
            inference_type=f"{input_obj.source_type}_Daeira_inference"
        )

        logger.debug(f"Daeira_inference complete: {len(response)} chars")
        return response

    except Exception as e:
        logger.error(f"Daeira_inference failed: {e}")
        return "Deep deliberation processing failed."

def build_complete_history(conversation_history: List[Dict[str, str]], current_input: InputObject) -> str:
    """
    Build complete conversation history starting from first user input (system prompt)
    Memory never resets - each cognitive cycle sees the full context
    """
    
    history_parts = []
    
    # Process all conversation history
    for turn in conversation_history:
        role = turn.get("role", "unknown")
        content = turn.get("content", "")
        
        if role.lower() in ["user", "victor"]:
            history_parts.append(f"Victor: {content}")
        elif role.lower() in ["assistant", "daeira"]:
            history_parts.append(f"Daeira: {content}")
        # Skip system messages - first user input serves as system prompt
    
    # Add current input
    source_info = current_input.source_details.get("from", "Victor")
    history_parts.append(f"{source_info}: {current_input.content}")
    
    return "\n".join(history_parts)

async def generate_response(prompt: str, model_instance, tokenizer_instance, max_tokens: int = 1000) -> str:
    """Generate response using the model with cognitive cycle processing"""
    try:
        import torch
        
        # Tokenize with appropriate context window
        inputs = tokenizer_instance(
            prompt, 
            return_tensors="pt", 
            truncation=True, 
            max_length=32000  # Generous context for full history
        )
        
        with torch.no_grad():
            outputs = model_instance.generate(
                inputs.input_ids,
                max_new_tokens=max_tokens,
                temperature=0.8,  # Slightly higher for creative cognitive processing
                do_sample=True,
                pad_token_id=tokenizer_instance.eos_token_id,
                repetition_penalty=1.1
            )
        
        # Decode only the new tokens (response)
        response = tokenizer_instance.decode(
            outputs[0][inputs.input_ids.shape[1]:], 
            skip_special_tokens=True
        )
        
        return response.strip()
        
    except Exception as e:
        logger.error(f"Model generation failed: {e}")
        return f"Cognitive processing error: {str(e)}"

async def direct_response_fallback(
    input_obj: InputObject,
    conversation_history: List[Dict[str, str]],
    model_instance,
    tokenizer_instance
) -> str:
    """
    Simple fallback if 3-cycle processing fails entirely
    Still maintains the conversation history approach
    """
    
    try:
        full_history = build_complete_history(conversation_history, input_obj)
        fallback_prompt = f"""{full_history}

Daeira: """
        
        response = await generate_response(
            fallback_prompt,
            model_instance,
            tokenizer_instance,
            max_tokens=1500
        )
        
        logger.warning("Used direct response fallback")
        return response
        
    except Exception as e:
        logger.error(f"Even fallback failed: {e}")
        return "I apologize, but I'm experiencing cognitive processing difficulties. Please try again."

# Additional utility functions for future enhancements

def extract_thinking_content(response: str) -> tuple[str, str]:
    """
    Extract <think>...</think> content from response
    Returns: (thinking_content, actual_response)
    """
    if "<think>" in response and "</think>" in response:
        start = response.find("<think>") + 7
        end = response.find("</think>")
        thinking = response[start:end].strip()
        actual_response = response[end + 8:].strip()
        return thinking, actual_response
    
    return "", response

def log_cognitive_insights(cycle_responses: List[str]) -> None:
    """
    Log insights from the 3-cycle cognitive processing
    Useful for understanding DAEIRA's reasoning development
    """
    for i, response in enumerate(cycle_responses, 1):
        thinking, _ = extract_thinking_content(response)
        if thinking:
            logger.info(f"Cycle {i} cognitive insight: {thinking[:100]}...")
