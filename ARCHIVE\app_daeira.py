import base64
from io import BytesIO
import json
import os

import gradio as gr
import torch
from app_modules.gradio_utils import (
    cancel_outputing,
    delete_last_conversation,
    reset_state,
    reset_textbox,
    transfer_input,
    wrap_gen_fn,
)
from app_modules.overwrites import reload_javascript
from app_modules.presets import CONCURRENT_COUNT, description, description_top, title
from app_modules.utils import configure_logger, is_variable_assigned, strip_stop_words

from daeria_inference import (
    convert_conversation_to_prompts,
    deepseek_generate,
    load_model,
)
from utils.conversation import SeparatorStyle


def load_models():
    model_dir = "C:/DAEIRA/deepseek_R1_7B"  # Fixed path
    return load_model(model_dir)


logger = configure_logger()
models = load_models()

# Add stop_words definition
stop_words = ["<|im_end|>", "</s>"]


def generate_prompt_with_history(
    text, history, vl_chat_processor, tokenizer, max_length=2048
):
    """
    Generate a prompt with history for the deepseek application (text-only).

    Args:
        text (str): The text prompt.
        history (list): List of previous conversation messages.
        tokenizer: The tokenizer used for encoding the prompt.
        max_length (int): The maximum length of the prompt.

    Returns:
        tuple: A tuple containing the generated prompt, conversation, and conversation copy. If the prompt could not be generated within the max_length limit, returns None.
    """

    # Load history from JSON file
    history = load_conversation_from_storage()

    sft_format = "deepseek"
    user_role_ind = 0
    bot_role_ind = 1

    # Initialize conversation
    conversation = vl_chat_processor.new_chat_template()

    if history:
        conversation.messages = history

    conversation.append_message(conversation.roles[user_role_ind], text)
    conversation.append_message(conversation.roles[bot_role_ind], "")

    # Create a copy of the conversation to avoid history truncation in the UI
    conversation_copy = conversation.copy()
    logger.info("=" * 80)
    logger.info(get_prompt(conversation))

    rounds = len(conversation.messages) // 2

    for _ in range(rounds):
        current_prompt = get_prompt(conversation)
        current_prompt = (
            current_prompt.replace("</s>", "")
            if sft_format == "deepseek"
            else current_prompt
        )

        if torch.tensor(tokenizer.encode(current_prompt)).size(-1) <= max_length:
            return conversation_copy

        if len(conversation.messages) % 2 != 0:
            gr.Error("The messages between user and assistant are not paired.")
            return

        try:
            for _ in range(2):  # pop out two messages in a row
                conversation.messages.pop(0)
        except IndexError:
            gr.Error("Input text processing failed, unable to respond in this round.")
            return None

    gr.Error("Prompt could not be generated within max_length limit.")
    return None


def to_gradio_chatbot(conv):
    """Convert the conversation to gradio chatbot format (for type='messages')."""
    # Gradio expects: [{"role": "user", "content": ...}, {"role": "assistant", "content": ...}, ...]
    role_map = {
        conv.roles[0]: "user",
        conv.roles[1]: "assistant",
    }
    messages = []
    for role, msg in conv.messages[conv.offset:]:
        if msg is not None:
            messages.append({"role": role_map.get(role, role.lower()), "content": msg})
    return messages


def to_gradio_history(conv):
    """Convert the conversation to gradio history state."""
    return conv.messages[conv.offset :]


def get_prompt(conv) -> str:
    """Get the prompt for generation."""
    # DeepSeek R1: Do not use system prompt, only user instructions.
    if conv.sep_style == SeparatorStyle.DeepSeek:
        seps = [conv.sep, conv.sep2]
        ret = ""
        for i, (role, message) in enumerate(conv.messages):
            if message:
                ret += role + ": " + message + seps[i % 2]
            else:
                ret += role + ":"
        return ret
    else:
        return conv.get_prompt


class ConversationStorage:
    def __init__(self, file_path):
        self.file_path = file_path
        # Ensure the file exists
        if not os.path.exists(self.file_path):
            with open(self.file_path, 'w') as f:
                json.dump([], f)

    def save_message(self, message):
        with open(self.file_path, 'r') as f:
            history = json.load(f)
        history.append(message)
        with open(self.file_path, 'w') as f:
            json.dump(history, f, indent=4)

    def get_history(self):
        with open(self.file_path, 'r') as f:
            return json.load(f)


# Initialize storage
conversation_storage = ConversationStorage("conversation_history.json")


def save_conversation_to_storage(conversation):
    for message in conversation.messages:
        conversation_storage.save_message(message)


def load_conversation_from_storage():
    return conversation_storage.get_history()


@wrap_gen_fn
def generate_response(
    text,
    chatbot,
    history,
    top_p,
    temperature,
    repetition_penalty,
    max_length_tokens,
    max_context_length_tokens,
):
    """Generate a response using the DeepSeek language model based on user input."""
    # Load and validate models
    try:
        tokenizer, vl_gpt, vl_chat_processor = models
        if text == "":
            yield chatbot, history, "Empty context."
            return
    except Exception:
        yield [[text, "No Model Found"]], [], "No Model Found"
        return

    conversation = generate_prompt_with_history(
        text,
        history,
        vl_chat_processor,
        tokenizer,
        max_length=max_context_length_tokens,
    )
    
    # Convert to model prompts
    prompts = convert_conversation_to_prompts(conversation)
    gradio_chatbot_output = to_gradio_chatbot(conversation)

    full_response = ""
    with torch.no_grad():
        for x in deepseek_generate(
            prompts=prompts,
            model=vl_gpt,
            tokenizer=tokenizer,
            stop_words=stop_words,
            max_length=max_length_tokens,
            temperature=temperature,
            repetition_penalty=repetition_penalty,
            top_p=top_p,
        ):
            # Enforce DeepSeek R1 reasoning pattern: start with <think>\n
            if not full_response:
                x = "<think>\n" + x.lstrip()
            full_response += x
            response = strip_stop_words(full_response, stop_words)
            conversation.update_last_message(response)
            # Update the last message dict's content
            if gradio_chatbot_output:
                gradio_chatbot_output[-1]["content"] = response
            yield gradio_chatbot_output, to_gradio_history(
                conversation
            ), "Generating..."

    # Save conversation to storage
    save_conversation_to_storage(conversation)

    print("flushed result to gradio")

    if is_variable_assigned("x"):
        print(f"{text}\n{'-' * 80}\n{x}\n{'=' * 80}")
        print(
            f"temperature: {temperature}, top_p: {top_p}, repetition_penalty: {repetition_penalty}, max_length_tokens: {max_length_tokens}"
        )

    yield gradio_chatbot_output, to_gradio_history(conversation), "Generate: Success"


def retry(
    text,
    chatbot,
    history,
    top_p,
    temperature,
    repetition_penalty,
    max_length_tokens,
    max_context_length_tokens,
):
    if len(history) == 0:
        yield (chatbot, history, "Empty context")
        return

    chatbot.pop()
    history.pop()
    text = history.pop()[-1]
    yield from generate_response(
        text,
        chatbot,
        history,
        top_p,
        temperature,
        repetition_penalty,
        max_length_tokens,
        max_context_length_tokens,
    )


def transfer_input(input_text):
    print("transferring input text")
    return (
        input_text,
        gr.update(value=""),
        gr.Button(visible=True),
    )


def build_demo(models):
    with open("assets/custom.css", "r", encoding="utf-8") as f:
        customCSS = f.read()

    with gr.Blocks(theme=gr.themes.Soft()) as demo:
        history = gr.State([])
        input_text = gr.State()

        with gr.Row():
            gr.HTML(title)
            status_display = gr.Markdown("Success", elem_id="status_display")
        gr.Markdown(description_top)

        with gr.Row(equal_height=True):
            with gr.Column(scale=4):
                with gr.Row():
                    chatbot = gr.Chatbot(
                        elem_id="DEXTER",
                        show_share_button=True,
                        type="messages",  # Set type for future compatibility
                        height=600,
                    )
                with gr.Row():
                    with gr.Column(scale=4):
                        text_box = gr.Textbox(
                            show_label=False, placeholder="Enter text", container=False
                        )
                    with gr.Column(
                        min_width=70,
                    ):
                        submitBtn = gr.Button("Send")
                    with gr.Column(
                        min_width=70,
                    ):
                        cancelBtn = gr.Button("Stop")
                with gr.Row():
                    emptyBtn = gr.Button(
                        "🧹 New Conversation",
                    )
                    retryBtn = gr.Button("🔄 Regenerate")
                    delLastBtn = gr.Button("🗑️ Remove Last Turn")

            with gr.Column():
                with gr.Tab(label="Parameter Setting") as parameter_row:
                    top_p = gr.Slider(
                        minimum=-0,
                        maximum=1.0,
                        value=0.95,
                        step=0.05,
                        interactive=True,
                        label="Top-p",
                    )
                    temperature = gr.Slider(
                        minimum=0,
                        maximum=1.0,
                        value=0.1,
                        step=0.1,
                        interactive=True,
                        label="Temperature",
                    )
                    repetition_penalty = gr.Slider(
                        minimum=0.0,
                        maximum=2.0,
                        value=1.1,
                        step=0.1,
                        interactive=True,
                        label="Repetition penalty",
                    )
                    max_length_tokens = gr.Slider(
                        minimum=0,
                        maximum=131072,
                        value=2048,
                        step=8,
                        interactive=True,
                        label="Max Generation Tokens",
                    )
                    max_context_length_tokens = gr.Slider(
                        minimum=0,
                        maximum=131072,
                        value=4096,
                        step=128,
                        interactive=True,
                        label="Max History Tokens",
                    )

        input_widgets = [
            input_text,
            chatbot,
            history,
            top_p,
            temperature,
            repetition_penalty,
            max_length_tokens,
            max_context_length_tokens,
        ]
        output_widgets = [chatbot, history, status_display]

        transfer_input_args = dict(
            fn=transfer_input,
            inputs=[text_box],
            outputs=[input_text, text_box, submitBtn],
            show_progress=True,
        )

        predict_args = dict(
            fn=generate_response,
            inputs=input_widgets,
            outputs=output_widgets,
            show_progress=True,
        )

        retry_args = dict(
            fn=retry,
            inputs=input_widgets,
            outputs=output_widgets,
            show_progress=True,
        )

        reset_args = dict(
            fn=reset_textbox, inputs=[], outputs=[text_box, status_display]
        )

        predict_events = [
            text_box.submit(**transfer_input_args).then(**predict_args),
            submitBtn.click(**transfer_input_args).then(**predict_args),
        ]

        emptyBtn.click(reset_state, outputs=output_widgets, show_progress=True)
        emptyBtn.click(**reset_args)
        retryBtn.click(**retry_args)

        delLastBtn.click(
            delete_last_conversation,
            [chatbot, history],
            output_widgets,
            show_progress=True,
        )

        cancelBtn.click(cancel_outputing, [], [status_display], cancels=predict_events)

    return demo


if __name__ == "__main__":
    demo = build_demo(models)
    demo.title = "DAEIRA"

    reload_javascript()
    demo.queue().launch(
        share=False,
        favicon_path="assets/favicon.ico",
        inbrowser=False,
        server_name="127.0.0.1",
        server_port=8000,
    )

    reload_javascript()
    demo.queue().launch(
        share=False,
        favicon_path="assets/favicon.ico",
        inbrowser=False,
        server_name="127.0.0.1",
        server_port=8000,
    )

