"""
Enhanced Document Study and Processing Module for DAHLIA - PROTOCOL COMPLIANT
Integrates cognitive document processing with traditional batch processing
"""
import os
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone
import uuid
import re
import hashlib
import asyncio

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import PyMuPDF for PDF processing
try:
    import fitz  # PyMuPDF
    PDF_PROCESSING_AVAILABLE = True
except ImportError:
    PDF_PROCESSING_AVAILABLE = False
    logger.warning("PyMuPDF not available - PDF processing will be limited")

# Import new cognitive processing components
# Note: These are commented out as they are not yet implemented
# from .cognitive_document_processor import CognitiveDocumentProcessor
# from .document_discovery import DocumentDiscoverySystem, DocumentRelationshipMapper

class EnhancedDocumentProcessor:
    """
    Enhanced document processor that combines traditional batch processing
    with cognitive, context-aware document analysis capabilities
    """
    
    def __init__(self, memory_manager=None, study_directory=None, model_instance=None, tokenizer_instance=None):
        self.memory_manager = memory_manager
        self.study_directory = study_directory or "C:\\DAHLIA\\STUDY"
        self.model_instance = model_instance
        self.tokenizer_instance = tokenizer_instance
        
        # Create study directory if it doesn't exist
        if not os.path.exists(self.study_directory):
            os.makedirs(self.study_directory)
            logger.info(f"Created study directory: {self.study_directory}")
        
        # Initialize processed files tracking
        self.processed_files = set()
        if memory_manager:
            self._create_processed_files_table()
            self._load_processed_files()
        
        # Initialize cognitive components if model is available
        self.cognitive_processor = None
        self.document_discovery = None
        self.relationship_mapper = None

        # Note: Cognitive components are not yet implemented
        # if model_instance and tokenizer_instance:
        #     try:
        #         self.cognitive_processor = CognitiveDocumentProcessor(
        #             model_instance=model_instance,
        #             tokenizer_instance=tokenizer_instance,
        #             memory_manager=memory_manager,
        #             study_directory=study_directory
        #         )
        #         logger.info("Cognitive document processor initialized")
        #     except Exception as e:
        #         logger.warning(f"Could not initialize cognitive processor: {e}")

        # self.document_discovery = DocumentDiscoverySystem(
        #     memory_manager=memory_manager,
        #     study_directory=study_directory
        # )

        # self.relationship_mapper = DocumentRelationshipMapper(study_directory)
        
        logger.info("Enhanced document processor initialized")
    
    def _create_processed_files_table(self):
        """Create table to track processed documents - NO PLACEHOLDERS"""
        if not self.memory_manager:
            return
        
        try:
            connection = self.memory_manager.structured_store.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ProcessedDocuments (
                file_path VARCHAR(500) PRIMARY KEY,
                file_hash VARCHAR(64) NOT NULL,
                processed_timestamp DATETIME(6) NOT NULL,
                document_event_id VARCHAR(36) NOT NULL,
                file_size BIGINT NOT NULL,
                processing_method VARCHAR(50) DEFAULT 'traditional',
                cognitive_analysis_available BOOLEAN DEFAULT FALSE,
                INDEX idx_processed_timestamp (processed_timestamp),
                INDEX idx_document_event_id (document_event_id),
                INDEX idx_processing_method (processing_method)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            connection.commit()
            logger.info("Enhanced ProcessedDocuments table created/verified")
            
        except Exception as e:
            logger.error(f"Error creating ProcessedDocuments table: {e}", exc_info=True)
            connection.rollback()
            raise
        finally:
            cursor.close()
            connection.close()
    
    def _load_processed_files(self):
        """Load list of already processed files from database - DEFINITIVE IMPLEMENTATION"""
        if not self.memory_manager:
            return
        
        try:
            connection = self.memory_manager.structured_store.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("SELECT file_path FROM ProcessedDocuments")
            results = cursor.fetchall()
            
            self.processed_files = {row[0] for row in results}
            logger.info(f"Loaded {len(self.processed_files)} processed files from database")
            
        except Exception as e:
            logger.error(f"Error loading processed files: {e}", exc_info=True)
            # Initialize empty set on error
            self.processed_files = set()
        finally:
            cursor.close()
            connection.close()
    
    def _get_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file content"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _record_processed_file(self, file_path: str, file_hash: str, document_event_id: str, processing_method: str = "traditional"):
        """Record that a file has been processed - DEFINITIVE STORAGE"""
        if not self.memory_manager:
            return
        
        try:
            connection = self.memory_manager.structured_store.get_connection()
            cursor = connection.cursor()
            
            file_size = os.path.getsize(file_path)
            cognitive_available = processing_method == "cognitive" or self.cognitive_processor is not None
            
            cursor.execute("""
            INSERT INTO ProcessedDocuments
            (file_path, file_hash, processed_timestamp, document_event_id, file_size, processing_method, cognitive_analysis_available)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            file_hash = VALUES(file_hash),
            processed_timestamp = VALUES(processed_timestamp),
            document_event_id = VALUES(document_event_id),
            file_size = VALUES(file_size),
            processing_method = VALUES(processing_method),
            cognitive_analysis_available = VALUES(cognitive_analysis_available)
            """, (
                file_path,
                file_hash,
                datetime.now(timezone.utc),
                document_event_id,
                file_size,
                processing_method,
                cognitive_available
            ))
            
            connection.commit()
            self.processed_files.add(file_path)
            logger.info(f"Recorded processed file: {os.path.basename(file_path)} ({processing_method})")
            
        except Exception as e:
            logger.error(f"Error recording processed file: {e}", exc_info=True)
            connection.rollback()
        finally:
            cursor.close()
            connection.close()
    
    # === COGNITIVE PROCESSING METHODS ===
    
    async def cognitive_process_for_query(
        self, 
        query: str, 
        max_documents: int = 3,
        processing_focus: str = "comprehensive"
    ) -> Dict[str, Any]:
        """
        Process documents with cognitive awareness for a specific query
        """
        if not self.cognitive_processor:
            logger.warning("Cognitive processor not available - falling back to discovery only")
            return await self._fallback_query_processing(query, max_documents)
        
        logger.info(f"Starting cognitive document processing for query: {query[:100]}...")
        
        try:
            # Discover relevant documents
            relevant_documents = await self.document_discovery.discover_relevant_documents(
                query=query,
                max_results=max_documents,
                include_relationships=True
            )
            
            if not relevant_documents:
                logger.info("No relevant documents found for query")
                return {
                    'success': True,
                    'processed_documents': [],
                    'message': 'No relevant documents found'
                }
            
            # Extract document paths
            doc_paths = [doc['document_path'] for doc in relevant_documents]
            
            # Process with cognitive analysis
            analysis_result = await self.cognitive_processor.analyze_documents_for_query(
                query=query,
                document_paths=doc_paths,
                processing_focus=processing_focus,
                max_documents=max_documents
            )
            
            # Update relationship mappings
            await self._update_document_relationships(doc_paths)
            
            logger.info(f"Cognitive processing completed: {analysis_result.get('document_count', 0)} documents")
            
            return {
                'success': True,
                'analysis_result': analysis_result,
                'discovered_documents': relevant_documents,
                'processing_method': 'cognitive',
                'query': query
            }
            
        except Exception as e:
            logger.error(f"Cognitive document processing failed: {e}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'processing_method': 'cognitive'
            }
    
    async def _fallback_query_processing(self, query: str, max_documents: int) -> Dict[str, Any]:
        """Fallback processing when cognitive processor is unavailable"""
        try:
            # Use discovery system only
            relevant_documents = await self.document_discovery.discover_relevant_documents(
                query=query,
                max_results=max_documents,
                include_relationships=False
            )
            
            return {
                'success': True,
                'discovered_documents': relevant_documents,
                'processing_method': 'discovery_only',
                'message': 'Cognitive analysis unavailable - discovery only'
            }
            
        except Exception as e:
            logger.error(f"Fallback query processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_method': 'fallback'
            }
    
    async def _update_document_relationships(self, document_paths: List[str]):
        """Update document relationship mappings based on processed documents"""
        try:
            if not self.relationship_mapper:
                return
            
            # Build relationships for processed documents
            for doc_path in document_paths:
                if os.path.exists(doc_path):
                    relationships = await self.relationship_mapper._analyze_document_relationships(
                        doc_path, document_paths
                    )
                    self.relationship_mapper.relationship_graph[doc_path] = set(relationships)
                    
        except Exception as e:
            logger.error(f"Error updating document relationships: {e}")
    
    # === TRADITIONAL PROCESSING METHODS (Enhanced) ===
    
    async def process_document(self, document_path: str, document_title: str = None, tags: List[str] = None) -> str:
        """
        Process a single document and add it to memory (enhanced with cognitive capabilities)
        """
        if not os.path.exists(document_path):
            raise FileNotFoundError(f"Document not found: {document_path}")
        
        # Generate default title from filename if not provided
        if not document_title:
            document_title = os.path.basename(document_path)
        
        # PROTOCOL 02 COMPLIANCE - NO PLACEHOLDERS
        if not tags:
            tags = []  # FIXED: was [str]
        
        logger.info(f"Processing document: {document_path}")
        
        try:
            # Calculate file hash for change detection
            file_hash = self._get_file_hash(document_path)
            
            # Read document content based on file type
            file_ext = os.path.splitext(document_path)[1].lower()
            
            if file_ext in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json']:
                # Text files - read directly
                with open(document_path, 'r', encoding='utf-8') as f:
                    document_content = f.read()
            elif file_ext == '.pdf':
                # Using PyMuPDF to extract text from PDF
                document_content = self._extract_pdf_text(document_path)
                logger.info(f"Extracted {len(document_content)} characters from PDF using PyMuPDF")
            else:
                # Try to read as text with fallback to binary
                try:
                    with open(document_path, 'r', encoding='utf-8') as f:
                        document_content = f.read()
                except UnicodeDecodeError:
                    logger.warning(f"File type {file_ext} not recognized as text, attempting binary read")
                    with open(document_path, 'rb') as f:
                        # Just read the first 500 bytes to identify file type
                        header = f.read(500)
                        document_content = f"[Binary file type: {file_ext}, not fully processed. Header: {header[:100].hex()}]"
            
            # Prepare metadata
            document_metadata = {
                "title": document_title,
                "path": document_path,
                "file_type": file_ext,
                "file_hash": file_hash,
                "file_size": os.path.getsize(document_path),
                "creation_time": datetime.now(timezone.utc).isoformat(),
                "study_method": "enhanced_processing",
                "tags": tags,
                "cognitive_capable": self.cognitive_processor is not None
            }
            
            # Add PDF-specific metadata if applicable
            if file_ext == '.pdf':
                pdf_metadata = self._extract_pdf_metadata(document_path)
                if pdf_metadata:
                    document_metadata.update(pdf_metadata)
            
            # Add to memory if memory manager is available
            if self.memory_manager:
                doc_id = self.memory_manager.add_document_to_memory(
                    document_content=document_content,
                    document_metadata=document_metadata,
                    chunking_strategy=self._enhanced_chunk_document
                )
                
                # Record as processed with enhanced method
                processing_method = "enhanced" if self.cognitive_processor else "traditional"
                self._record_processed_file(document_path, file_hash, doc_id, processing_method)
                
                logger.info(f"Document processed and added to memory with ID: {doc_id}")
                return doc_id
            else:
                logger.error("Memory manager not available - document cannot be stored")
                raise RuntimeError("Memory manager not initialized")
            
        except Exception as e:
            logger.error(f"Error processing document {document_path}: {e}", exc_info=True)
            raise
    
    def _extract_pdf_text(self, pdf_path: str) -> str:
        """
        Extract text content from PDF files using PyMuPDF
        """
        if not PDF_PROCESSING_AVAILABLE:
            return f"[PDF processing not available - PyMuPDF not installed. File: {os.path.basename(pdf_path)}]"

        try:
            text_content = ""

            # Open the PDF file
            with fitz.open(pdf_path) as pdf_document:
                # Get number of pages
                num_pages = len(pdf_document)
                logger.info(f"PDF has {num_pages} pages")

                # Extract text from each page
                for page_num in range(num_pages):
                    page = pdf_document[page_num]
                    page_text = page.get_text()

                    # Add page number reference and page text
                    text_content += f"\n\n--- Page {page_num + 1} ---\n\n"
                    text_content += page_text

            return text_content

        except Exception as e:
            logger.error(f"Error extracting text from PDF {pdf_path}: {e}", exc_info=True)
            return f"[Error extracting PDF text: {str(e)}]"
    
    def _extract_pdf_metadata(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract metadata from PDF files using PyMuPDF
        """
        if not PDF_PROCESSING_AVAILABLE:
            return {"pdf_processing_available": False}

        try:
            pdf_metadata = {}

            with fitz.open(pdf_path) as pdf_document:
                # Extract document metadata
                metadata = pdf_document.metadata

                if metadata:
                    # Common PDF metadata fields
                    for key in ["title", "author", "subject", "keywords", "creator", "producer"]:
                        if metadata.get(key):
                            pdf_metadata[f"pdf_{key}"] = metadata[key]

                    # PDF creation and modification dates
                    if "creationDate" in metadata:
                        pdf_metadata["pdf_creation_date"] = metadata["creationDate"]
                    if "modDate" in metadata:
                        pdf_metadata["pdf_modification_date"] = metadata["modDate"]

                # Add basic document structure info
                pdf_metadata["pdf_page_count"] = len(pdf_document)

                # Check if the PDF has a table of contents
                toc = pdf_document.get_toc()
                pdf_metadata["pdf_has_toc"] = len(toc) > 0

                # Check if the PDF has form fields
                pdf_metadata["pdf_has_forms"] = False

            return pdf_metadata

        except Exception as e:
            logger.error(f"Error extracting metadata from PDF {pdf_path}: {e}", exc_info=True)
            return {}
    
    def _enhanced_chunk_document(self, text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Enhanced chunking strategy that considers document structure
        """
        chunks = []

        # First, try to identify document structure
        sections = self._identify_document_sections(text)
        
        if sections and len(sections) > 1:
            # Use structure-aware chunking
            for section in sections:
                section_chunks = self._chunk_section(section, chunk_size, overlap)
                chunks.extend(section_chunks)
        else:
            # Fall back to standard chunking
            chunks = self._standard_chunk_document(text, chunk_size, overlap)
        
        logger.info(f"Document chunked into {len(chunks)} segments using enhanced strategy")
        return chunks
    
    def _identify_document_sections(self, text: str) -> List[str]:
        """Identify document sections based on headers and structure"""
        # Look for markdown-style headers
        markdown_sections = re.split(r'\n#{1,6}\s+', text)
        if len(markdown_sections) > 2:
            return [section.strip() for section in markdown_sections if section.strip()]
        
        # Look for numbered sections
        numbered_sections = re.split(r'\n\d+\.?\s+', text)
        if len(numbered_sections) > 2:
            return [section.strip() for section in numbered_sections if section.strip()]
        
        # Look for paragraph breaks (double newlines)
        paragraph_sections = text.split('\n\n')
        if len(paragraph_sections) > 5:
            # Group small paragraphs together
            grouped_sections = []
            current_section = ""
            
            for paragraph in paragraph_sections:
                if len(current_section + paragraph) < 800:
                    current_section += paragraph + "\n\n"
                else:
                    if current_section:
                        grouped_sections.append(current_section.strip())
                    current_section = paragraph + "\n\n"
            
            if current_section:
                grouped_sections.append(current_section.strip())
            
            return grouped_sections
        
        return []
    
    def _chunk_section(self, section: str, chunk_size: int, overlap: int) -> List[str]:
        """Chunk a single section while preserving context"""
        if len(section) <= chunk_size:
            return [section]
        
        return self._standard_chunk_document(section, chunk_size, overlap)
    
    def _standard_chunk_document(self, text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Standard chunking strategy (enhanced from original)
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            
            # Try to find a good break point (paragraph or sentence)
            if end < len(text) and end - start == chunk_size:
                # Look for paragraph break first
                paragraph_break = text.rfind("\n\n", start + chunk_size - overlap, end)
                if paragraph_break != -1:
                    end = paragraph_break + 2
                else:
                    # Look for line break
                    line_break = text.rfind("\n", start + chunk_size - overlap, end)
                    if line_break != -1:
                        end = line_break + 1
                    else:
                        # Look for sentence break
                        sentence_break = text.rfind(". ", start + chunk_size - overlap, end)
                        if sentence_break != -1:
                            end = sentence_break + 2
            
            chunks.append(text[start:end])
            start = end - overlap if end < len(text) else end
        
        return chunks
    
    async def scan_study_directory(self, use_cognitive: bool = False) -> List[str]:
        """
        Enhanced directory scanning with optional cognitive processing
        """
        if not os.path.exists(self.study_directory):
            logger.warning(f"Study directory does not exist: {self.study_directory}")
            return []
        
        processed_ids = []
        
        try:
            # Get list of files in study directory
            files = [f for f in os.listdir(self.study_directory) 
                     if os.path.isfile(os.path.join(self.study_directory, f))]
            
            # Process new files
            for filename in files:
                file_path = os.path.join(self.study_directory, filename)
                
                # Skip already processed files
                if file_path in self.processed_files:
                    continue
                
                try:
                    # Process the document using enhanced method
                    doc_id = await self.process_document(
                        document_path=file_path,
                        document_title=None,  # Use default (filename)
                        tags=["auto_study", "background", "enhanced"]
                    )
                    
                    processed_ids.append(doc_id)
                    logger.info(f"Auto-processed document: {filename}")
                    
                except Exception as e:
                    logger.error(f"Error auto-processing document {filename}: {e}")
            
            # Build relationship map if cognitive processing is enabled
            if use_cognitive and self.relationship_mapper:
                try:
                    await self.relationship_mapper.build_relationship_map()
                    logger.info("Document relationship map updated")
                except Exception as e:
                    logger.error(f"Error building relationship map: {e}")
            
            if not processed_ids:
                logger.info("No new documents found in study directory")
            else:
                logger.info(f"Processed {len(processed_ids)} new documents from study directory")
            
            return processed_ids
            
        except Exception as e:
            logger.error(f"Error scanning study directory: {e}", exc_info=True)
            return []
    
    # === UTILITY AND MANAGEMENT METHODS ===
    
    def get_processing_capabilities(self) -> Dict[str, bool]:
        """Get information about available processing capabilities"""
        return {
            'traditional_processing': True,
            'cognitive_processing': self.cognitive_processor is not None,
            'document_discovery': self.document_discovery is not None,
            'relationship_mapping': self.relationship_mapper is not None,
            'enhanced_chunking': True,
            'memory_integration': self.memory_manager is not None
        }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        stats = {
            'processed_files_count': len(self.processed_files),
            'study_directory': self.study_directory,
            'capabilities': self.get_processing_capabilities()
        }
        
        if self.cognitive_processor:
            stats.update(self.cognitive_processor.get_cache_stats())
        
        return stats
    
    def clear_caches(self):
        """Clear all processing caches"""
        if self.cognitive_processor:
            self.cognitive_processor.clear_cache()
        
        if self.document_discovery:
            self.document_discovery.clear_caches()
        
        logger.info("All document processing caches cleared")
    
    async def reprocess_document_with_cognitive(self, document_path: str, query: str = None) -> Dict[str, Any]:
        """
        Reprocess a document using cognitive analysis
        """
        if not self.cognitive_processor:
            return {'success': False, 'error': 'Cognitive processor not available'}
        
        if not os.path.exists(document_path):
            return {'success': False, 'error': 'Document not found'}
        
        try:
            if query:
                # Process with specific query context
                result = await self.cognitive_processor.analyze_documents_for_query(
                    query=query,
                    document_paths=[document_path],
                    processing_focus="comprehensive",
                    max_documents=1
                )
            else:
                # Process with general analysis
                result = await self.cognitive_processor._process_single_document(
                    doc_path=document_path,
                    query="Provide comprehensive analysis of this document",
                    processing_focus="comprehensive"
                )
            
            return {
                'success': True,
                'analysis': result,
                'document_path': document_path,
                'processing_method': 'cognitive_reprocessing'
            }
            
        except Exception as e:
            logger.error(f"Error reprocessing document with cognitive analysis: {e}")
            return {'success': False, 'error': str(e)}


# Maintain backward compatibility
class DocumentProcessor(EnhancedDocumentProcessor):
    """
    Backward compatibility wrapper for the original DocumentProcessor
    """
    
    def __init__(self, memory_manager=None, study_directory=None):
        # Initialize without cognitive components for backward compatibility
        super().__init__(
            memory_manager=memory_manager,
            study_directory=study_directory,
            model_instance=None,
            tokenizer_instance=None
        )
        logger.info("DocumentProcessor initialized in compatibility mode")


# Factory function for enhanced initialization
def create_enhanced_document_processor(
    memory_manager=None, 
    study_directory=None, 
    model_instance=None, 
    tokenizer_instance=None
) -> EnhancedDocumentProcessor:
    """
    Factory function to create an enhanced document processor
    """
    return EnhancedDocumentProcessor(
        memory_manager=memory_manager,
        study_directory=study_directory,
        model_instance=model_instance,
        tokenizer_instance=tokenizer_instance
    )