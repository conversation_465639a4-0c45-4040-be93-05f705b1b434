"""
Comprehensive Prompt Logging System for DAEIRA
Saves all prompts, responses, and metadata for analysis and debugging
"""

import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
# Environment variables removed - using hardcoded configuration

logger = logging.getLogger(__name__)

class PromptLogger:
    """Centralized prompt logging system for DAEIRA"""
    
    def __init__(self):
        self.prompt_save_dir = "C:/DAEIRA/PROMPTS"
        self.inference_save_dir = "C:/DAEIRA/INFERENCE"
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Create prompt and inference directories if they don't exist"""
        for directory in [self.prompt_save_dir, self.inference_save_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                logger.info(f"Created directory: {directory}")
    
    def save_prompt(self,
                   prompt_text: str,
                   prompt_type: str,
                   metadata: Optional[Dict[str, Any]] = None,
                   session_id: Optional[str] = None) -> str:
        """
        Save a prompt as plain text only (no metadata)

        Args:
            prompt_text: The actual prompt text
            prompt_type: Type of prompt (chat, email_triage, email_response, document, etc.)
            metadata: Ignored - kept for compatibility
            session_id: Ignored - kept for compatibility

        Returns:
            Path to saved file
        """
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S_%f")
            filename = f"{prompt_type}_{timestamp}.txt"
            filepath = os.path.join(self.prompt_save_dir, filename)

            # Save only the plain prompt text
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(prompt_text)

            logger.info(f"Prompt saved: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Failed to save prompt: {e}", exc_info=True)
            return None
    
    def save_inference_result(self,
                            prompt_text: str,
                            response_text: str,
                            inference_type: str,
                            metadata: Optional[Dict[str, Any]] = None,
                            session_id: Optional[str] = None,
                            event_id: Optional[str] = None) -> str:
        """
        Save complete inference result as plain text only (no metadata)

        Args:
            prompt_text: The input prompt
            response_text: The model's response
            inference_type: Type of inference (preva_Daeira_inference, eva_Daeira_inference, etc.)
            metadata: Ignored - kept for compatibility
            session_id: Ignored - kept for compatibility
            event_id: Ignored - kept for compatibility

        Returns:
            Path to saved file
        """
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S_%f")
            filename = f"{inference_type}_{timestamp}.txt"
            filepath = os.path.join(self.inference_save_dir, filename)

            # Save only the plain prompt and response text
            with open(filepath, "w", encoding="utf-8") as f:
                f.write("=== PROMPT ===\n")
                f.write(prompt_text)
                f.write("\n\n=== RESPONSE ===\n")
                f.write(response_text)

            logger.info(f"Inference result saved: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Failed to save inference result: {e}", exc_info=True)
            return None
    
    def save_email_prompt(self,
                         prompt_text: str,
                         email_type: str,
                         email_metadata: Dict[str, Any]) -> str:
        """
        Save email-related prompts with email-specific metadata
        
        Args:
            prompt_text: The prompt text
            email_type: Type (triage, response, etc.)
            email_metadata: Email-specific metadata (from, subject, etc.)
            
        Returns:
            Path to saved file
        """
        metadata = {
            "email_type": email_type,
            "from_address": email_metadata.get("from_address"),
            "subject": email_metadata.get("subject"),
            "message_id": email_metadata.get("message_id")
        }
        
        return self.save_prompt(
            prompt_text=prompt_text,
            prompt_type=f"email_{email_type}",
            metadata=metadata,
            session_id=email_metadata.get("event_id")
        )
    
    def save_chat_prompt(self,
                        prompt_text: str,
                        session_id: str,
                        user_input: str) -> str:
        """
        Save chat prompts with chat-specific metadata
        
        Args:
            prompt_text: The full prompt with history
            session_id: Chat session ID
            user_input: The user's input
            
        Returns:
            Path to saved file
        """
        metadata = {
            "user_input": user_input,
            "has_history": "Victor:" in prompt_text or "Daeira:" in prompt_text
        }
        
        return self.save_prompt(
            prompt_text=prompt_text,
            prompt_type="chat",
            metadata=metadata,
            session_id=session_id
        )
    
    def save_document_prompt(self,
                           prompt_text: str,
                           document_path: str,
                           processing_type: str) -> str:
        """
        Save document processing prompts
        
        Args:
            prompt_text: The prompt text
            document_path: Path to the document being processed
            processing_type: Type of processing (analysis, summary, etc.)
            
        Returns:
            Path to saved file
        """
        metadata = {
            "document_path": document_path,
            "document_name": os.path.basename(document_path),
            "processing_type": processing_type
        }
        
        return self.save_prompt(
            prompt_text=prompt_text,
            prompt_type="document",
            metadata=metadata
        )
    
    def get_prompt_stats(self) -> Dict[str, Any]:
        """Get statistics about saved prompts"""
        try:
            prompt_files = []
            inference_files = []
            
            if os.path.exists(self.prompt_save_dir):
                prompt_files = [f for f in os.listdir(self.prompt_save_dir) if f.endswith('.txt')]
            
            if os.path.exists(self.inference_save_dir):
                inference_files = [f for f in os.listdir(self.inference_save_dir) if f.endswith('.txt')]
            
            return {
                "prompt_save_dir": self.prompt_save_dir,
                "inference_save_dir": self.inference_save_dir,
                "total_prompts": len(prompt_files),
                "total_inferences": len(inference_files),
                "recent_prompts": sorted(prompt_files)[-5:] if prompt_files else [],
                "recent_inferences": sorted(inference_files)[-5:] if inference_files else []
            }
            
        except Exception as e:
            logger.error(f"Failed to get prompt stats: {e}")
            return {"error": str(e)}

# Global instance
prompt_logger = PromptLogger()

# Convenience functions
def save_prompt(prompt_text: str, prompt_type: str, **kwargs) -> str:
    """Convenience function to save a prompt"""
    return prompt_logger.save_prompt(prompt_text, prompt_type, **kwargs)

def save_inference_result(prompt_text: str, response_text: str, inference_type: str, **kwargs) -> str:
    """Convenience function to save inference result"""
    return prompt_logger.save_inference_result(prompt_text, response_text, inference_type, **kwargs)

def save_email_prompt(prompt_text: str, email_type: str, email_metadata: Dict[str, Any]) -> str:
    """Convenience function to save email prompt"""
    return prompt_logger.save_email_prompt(prompt_text, email_type, email_metadata)

def save_chat_prompt(prompt_text: str, session_id: str, user_input: str) -> str:
    """Convenience function to save chat prompt"""
    return prompt_logger.save_chat_prompt(prompt_text, session_id, user_input)

def save_document_prompt(prompt_text: str, document_path: str, processing_type: str) -> str:
    """Convenience function to save document prompt"""
    return prompt_logger.save_document_prompt(prompt_text, document_path, processing_type)
